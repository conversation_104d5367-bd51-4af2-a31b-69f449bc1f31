apiVersion: v1
kind: ConfigMap
metadata:
  name: sas-agent-kafka-configmap
data:
  airbank.ci360.proxy.host: ''
  airbank.ci360.proxy.port: ''
  airbank.ci360.proxy.username: ''
  airbank.ci360.url: http://external-services-mock.ingress.perf.np.ab/sas360/marketingGateway/events
  kafka.ab.topics.generalContract: perf.cz.airbank.ams.generalcontract.application.change.v1
  kafka.ab.topics.customerRelation: perf.cz.airbank.obs.generalcontract.customerrelations.change.v1
  kafka.ab.topics.consent: perf.cz.airbank.cml.marketing.consents.change.v1
  kafka.ab.topics.loanApplication: perf.cz.airbank.ams.cashloan.application.status.v1
  kafka.ab.topics.consolidationApplication: perf.cz.airbank.ams.consolidation.application.status.v1
  kafka.ab.topics.overdraftApplication: perf.cz.airbank.ams.overdraft.application.status.v1
  kafka.ab.topics.mortgageApplication: perf.cz.airbank.ams.mortgage.application.status.v1
  kafka.ab.topics.mortgageRefApplication: perf.cz.airbank.ams.mortgageref.application.status.v1
  kafka.ab.topics.splitPaymentApplication: perf.cz.airbank.ams.splitpayment.application.status.v1
  kafka.ab.topics.travelInsuranceApplication: perf.cz.airbank.ams.travelinsurance.application.status.v1
  kafka.ab.topics.accountApplicationStatus: perf.cz.airbank.ams.account.application.status.v1
  kafka.ab.topics.stockEtfApplicationStatus: perf.cz.airbank.ams.stocketf.application.status.v1
  kafka.ab.topics.transactions: perf.cz.airbank.obs.transaction.customertransaction.v2
  kafka.ab.topics.loanProductStatusChange: perf.cz.airbank.obs.loan.loanstatuschange.v1
  spring.datasource.url: ********************************************************
  kafka.ab.topics.pensionApplication: perf.cz.airbank.ams.pensionstatus.application.change.v1
  kafka.ab.topics.investmentApplication: perf.cz.airbank.ams.investmentsstatus.application.change.v1
  kafka.ab.topics.sas360messageResult: perf.cz.airbank.sas.campaign.result.v1
  airbank.kafka.sas360messageResult.topics: perf.cz.airbank.sas.campaign.result.v1
  kafka.ab.topics.successfulDevicePairing: perf.cz.airbank.rmd.devicepairing.success.v1
  kafka.ab.topics.cardDigitalization: perf.cz.airbank.cms.card.digitization.new.v1
  kafka.ab.topics.airbankClientUnityMemberPartyRemoved: perf.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
  kafka.ab.topics.airbankClientUnityMemberPartyDeactivated: perf.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
  kafka.ab.topics.airbankClientUnityMemberDeactivated: perf.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
  kafka.ab.topics.plannedCallCreated: perf.cz.airbank.cml.planned.call.created.v1
  wsc.o2proxy.unity.url: http://o2-proxy.ingress.perf.np.ab/ws/sas-agent-kafka/unity
