<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/overdraft"
                  targetNamespace="http://airbank.cz/ams/ws/application/overdraft">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/overdraft">
            <xs:include schemaLocation="AMSOverdraftApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <wsdl:message name="InitOverdraftParametersRequest">
        <wsdl:part element="InitOverdraftParametersRequest" name="InitOverdraftParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftParametersResponse">
        <wsdl:part element="InitOverdraftParametersResponse" name="InitOverdraftParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOverdraftParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOverdraftParametersRequest">
        <wsdl:part element="UpdateOverdraftParametersRequest" name="UpdateOverdraftParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftParametersResponse">
        <wsdl:part element="UpdateOverdraftParametersResponse" name="UpdateOverdraftParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOverdraftParametersFault"/>
    </wsdl:message>

    <wsdl:message name="InitOverdraftAcceptedRequest">
        <wsdl:part element="InitOverdraftAcceptedRequest" name="InitOverdraftAcceptedRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftAcceptedResponse">
        <wsdl:part element="InitOverdraftAcceptedResponse" name="InitOverdraftAcceptedResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftAcceptedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOverdraftAcceptedFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOverdraftAcceptedRequest">
        <wsdl:part element="UpdateOverdraftAcceptedRequest" name="UpdateOverdraftAcceptedRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftAcceptedResponse">
        <wsdl:part element="UpdateOverdraftAcceptedResponse" name="UpdateOverdraftAcceptedResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftAcceptedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOverdraftAcceptedFault"/>
    </wsdl:message>

    <wsdl:message name="InitOverdraftSummaryRequest">
        <wsdl:part element="InitOverdraftSummaryRequest" name="InitOverdraftSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftSummaryResponse">
        <wsdl:part element="InitOverdraftSummaryResponse" name="InitOverdraftSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOverdraftSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOverdraftSummaryRequest">
        <wsdl:part element="UpdateOverdraftSummaryRequest" name="UpdateOverdraftSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftSummaryResponse">
        <wsdl:part element="UpdateOverdraftSummaryResponse" name="UpdateOverdraftSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOverdraftSummaryFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Request to resume loan application-->
    <wsdl:message name="ResumeApplicationRequest">
        <wsdl:part element="ResumeApplicationRequest" name="ResumeApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationResponse">
        <wsdl:part element="ResumeApplicationResponse" name="ResumeApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ResumeApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="InitCampaignPromoRequest">
        <wsdl:part element="InitCampaignPromoRequest" name="InitCampaignPromoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCampaignPromoResponse">
        <wsdl:part element="InitCampaignPromoResponse" name="InitCampaignPromoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCampaignPromoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCampaignPromoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCampaignPromoRequest">
        <wsdl:part element="UpdateCampaignPromoRequest" name="UpdateCampaignPromoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCampaignPromoResponse">
        <wsdl:part element="UpdateCampaignPromoResponse" name="UpdateCampaignPromoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCampaignPromoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCampaignPromoFault"/>
    </wsdl:message>

    <wsdl:portType name="OverdraftApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for overdraft. Uses cuid and idProfile from tracking context to
                identify customer and profile.

            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOverdraftParameters">
            <wsdl:documentation>Gets product parameters.

            </wsdl:documentation>
            <wsdl:input message="InitOverdraftParametersRequest"/>

            <wsdl:output message="InitOverdraftParametersResponse"/>
            <wsdl:fault name="InitOverdraftParametersFault" message="InitOverdraftParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOverdraftParameters">
            <wsdl:documentation>Saves paramaters that were selected by client.

            </wsdl:documentation>
            <wsdl:input message="UpdateOverdraftParametersRequest"/>

            <wsdl:output message="UpdateOverdraftParametersResponse"/>
            <wsdl:fault name="UpdateOverdraftParametersFault" message="UpdateOverdraftParametersFault"/>
        </wsdl:operation>

       <wsdl:operation name="InitOverdraftAccepted">
            <wsdl:documentation>Init of overdraft accepted screen in full amount.

            </wsdl:documentation>
            <wsdl:input message="InitOverdraftAcceptedRequest"/>

            <wsdl:output message="InitOverdraftAcceptedResponse"/>
            <wsdl:fault name="InitOverdraftAcceptedFault" message="InitOverdraftAcceptedFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftAccepted">
            <wsdl:documentation>Operation used to update view of scoring result.

            </wsdl:documentation>
            <wsdl:input message="UpdateOverdraftAcceptedRequest"/>

            <wsdl:output message="UpdateOverdraftAcceptedResponse"/>
            <wsdl:fault name="UpdateOverdraftAcceptedFault" message="UpdateOverdraftAcceptedFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOverdraftSummary">
            <wsdl:documentation>Operation used to init overdraft summary.

            </wsdl:documentation>
            <wsdl:input message="InitOverdraftSummaryRequest"/>

            <wsdl:output message="InitOverdraftSummaryResponse"/>
            <wsdl:fault name="InitOverdraftSummaryFault" message="InitOverdraftSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftSummary">
            <wsdl:documentation>Operation used to update overdraft summary.

            </wsdl:documentation>
            <wsdl:input message="UpdateOverdraftSummaryRequest"/>

            <wsdl:output message="UpdateOverdraftSummaryResponse"/>
            <wsdl:fault name="UpdateOverdraftSummaryFault" message="UpdateOverdraftSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <wsdl:documentation>Resumes process of overdraft application. Use it instead of GetCurrentTask because this will also update actual task and restart
                scoring, if previous one expired.
            </wsdl:documentation>
            <wsdl:input message="ResumeApplicationRequest"/>
            <wsdl:output message="ResumeApplicationResponse"/>
            <wsdl:fault name="ResumeApplicationFault" message="ResumeApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCampaignPromo">
            <wsdl:documentation>Operation used to init campaign promo.

            </wsdl:documentation>
            <wsdl:input message="InitCampaignPromoRequest"/>

            <wsdl:output message="InitCampaignPromoResponse"/>
            <wsdl:fault name="InitCampaignPromoFault" message="InitCampaignPromoFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateCampaignPromo">
            <wsdl:documentation>Operation used to update campaign promo.

            </wsdl:documentation>
            <wsdl:input message="UpdateCampaignPromoRequest"/>

            <wsdl:output message="UpdateCampaignPromoResponse"/>
            <wsdl:fault name="UpdateCampaignPromoFault" message="UpdateCampaignPromoFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="OverdraftApplicationBinding" type="OverdraftApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitOverdraftParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOverdraftParametersFault">
                <soap:fault name="InitOverdraftParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOverdraftParametersFault">
                <soap:fault name="UpdateOverdraftParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOverdraftAccepted">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOverdraftAcceptedFault">
                <soap:fault name="InitOverdraftAcceptedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftAccepted">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOverdraftAcceptedFault">
                <soap:fault name="UpdateOverdraftAcceptedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOverdraftSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOverdraftSummaryFault">
                <soap:fault name="InitOverdraftSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOverdraftSummaryFault">
                <soap:fault name="UpdateOverdraftSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ResumeApplicationFault">
                <soap:fault name="ResumeApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCampaignPromo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCampaignPromoFault">
                <soap:fault name="InitCampaignPromoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCampaignPromo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCampaignPromoFault">
                <soap:fault name="UpdateCampaignPromoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="OverdraftApplicationService">
        <wsdl:documentation>Service is providing operations related to existing customer overdraft application.


            Standalone calls:
            - getCurrentTask - returns current task (step) of loan application process
            - cancel - cancels loan application
        </wsdl:documentation>
        <wsdl:port name="OverdraftApplicationPort" binding="OverdraftApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/overdraft"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>