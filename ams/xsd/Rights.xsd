<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           xmlns="http://airbank.cz/ams/ws/backoffice/rights"
           targetNamespace="http://airbank.cz/ams/ws/backoffice/rights"
           jxb:version="2.1" elementFormDefault="qualified">

  <!-- WARNING: THE CONTENT OF THIS FILE IS GENERATED, DO NOT EDIT IT. Use AMS RightXsdWriter.java to generate its content (see pom.xml). -->

    <xs:simpleType name="Right">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ROLE_ACCESS_AMG"/>
            <xs:enumeration value="ROLE_ACCESS_UW"/>
            <xs:enumeration value="ROLE_DASHBOARD_APPLICATIONS"/>
            <xs:enumeration value="ROLE_DASHBOARD_MA"/>
            <xs:enumeration value="ROLE_MA_STANDARD_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_EXPERT_INCOME_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_SPECIFIC_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_VIP_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_MANUAL_PAIRING_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_CHECK_LAND_REGISTRY_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_CHECK_DOCUMENTS_INCOME_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_CHECK_MATERIAL_FOR_DRAWN"/>
            <xs:enumeration value="ROLE_MA_PRE_DRAWN_CHECK_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_FINAL_DRAWN_CHECK_MORTGAGE"/>
            <xs:enumeration value="ROLE_MA_PREPARE_MORTGAGE_DOCUMENTATION"/>
            <xs:enumeration value="ROLE_MA_CHECK_MORTGAGE_DOCUMENTATION"/>
            <xs:enumeration value="ROLE_MA_PUBLISH_MORTGAGE_DOCUMENTATION"/>
            <xs:enumeration value="ROLE_MA_FAIL_TO_FINISH"/>
            <xs:enumeration value="ROLE_EDIT_BUSINESS_OWNER"/>
            <xs:enumeration value="ROLE_EDIT_UW_OWNER"/>
            <xs:enumeration value="ROLE_EDIT_APPLICATION_DATA"/>
            <xs:enumeration value="ROLE_EDIT_EMPLOYMENT"/>
            <xs:enumeration value="ROLE_EDIT_OBS_CALC_STD_DATA"/>
            <xs:enumeration value="ROLE_EDIT_OBS_CALC_UTIL_DATE"/>
            <xs:enumeration value="ROLE_EDIT_COMMITMENT_STD_DATA"/>
            <xs:enumeration value="ROLE_EDIT_COMMITMENT_REMAINING_AMOUNT"/>
            <xs:enumeration value="ROLE_EDIT_CHECKS_NON_BO"/>
            <xs:enumeration value="ROLE_EDIT_CHECKS_BO"/>
            <xs:enumeration value="ROLE_RUN_GENERATE_DOC"/>
            <xs:enumeration value="ROLE_EDIT_NOTES"/>
            <xs:enumeration value="ROLE_EDIT_CLIENT_CONTACT"/>
            <xs:enumeration value="ROLE_SHOW_RISK_DATA"/>
            <xs:enumeration value="ROLE_RUN_ADHOC_SCORING"/>
            <xs:enumeration value="ROLE_EDIT_SCORING"/>
            <xs:enumeration value="ROLE_EDIT_CHANNELS_DOCUMENTS"/>
            <xs:enumeration value="ROLE_RUN_SEND_TO_OVR"/>
            <xs:enumeration value="ROLE_RUN_POSTPONE_MA"/>
            <xs:enumeration value="ROLE_RUN_RESTART_APP"/>
            <xs:enumeration value="ROLE_RUN_REJECT"/>
            <xs:enumeration value="ROLE_RUN_CANCEL"/>
            <xs:enumeration value="ROLE_RUN_FINISH_MA"/>
            <xs:enumeration value="ROLE_SHOW_KO_WARNINGS"/>
            <xs:enumeration value="ROLE_SHOW_HISTORY"/>
            <xs:enumeration value="ROLE_DASHBOARD_TEAMLEADER"/>
            <xs:enumeration value="ROLE_DASHBOARD_GENERAL"/>
            <xs:enumeration value="ROLE_MA_CREDIT_EXPERT"/>
            <xs:enumeration value="ROLE_MA_EXPERT_INCOME"/>
            <xs:enumeration value="ROLE_MA_VIP"/>
            <xs:enumeration value="ROLE_MA_EXPERT_CONSOLIDATION"/>
            <xs:enumeration value="ROLE_MA_PILOTY"/>
            <xs:enumeration value="ROLE_MA_UW_COMPLETION"/>
            <xs:enumeration value="ROLE_MA_MANUAL_PAIRING"/>
            <xs:enumeration value="ROLE_MA_VER"/>
            <xs:enumeration value="ROLE_MA_RISK_CASE"/>
            <xs:enumeration value="ROLE_MA_SPECIFIC_CASE"/>
            <xs:enumeration value="ROLE_MA_COMPLETION"/>
            <xs:enumeration value="ROLE_MA_DETAIL_UW"/>
            <xs:enumeration value="ROLE_MA_DETAIL_COMP"/>
            <xs:enumeration value="ROLE_MA_DETAIL_VER"/>
            <xs:enumeration value="ROLE_MA_DETAIL_MANUAL_PAIRING"/>
            <xs:enumeration value="ROLE_MANUAL_ASSIGNMENT"/>
            <xs:enumeration value="ROLE_SET_PRIORITY"/>
            <xs:enumeration value="ROLE_CREATE_EVENT"/>
            <xs:enumeration value="ROLE_EDIT_EVENT"/>
            <xs:enumeration value="ROLE_SHOW_EVENT_DETAIL"/>
            <xs:enumeration value="ROLE_CHANGE_APPROVER"/>
            <xs:enumeration value="ROLE_EDIT_PRIVATE_NOTES"/>
            <xs:enumeration value="ROLE_ACCESS_SUMARIZATION_UW"/>
            <xs:enumeration value="ROLE_ACCESS_PERSONAL_QUEUE"/>
            <xs:enumeration value="ROLE_ACCESS_PUBLIC_QUEUE_UW"/>
            <xs:enumeration value="ROLE_ACCESS_PUBLIC_QUEUE_COMP"/>
            <xs:enumeration value="ROLE_ACCESS_PUBLIC_QUEUE_VER"/>
            <xs:enumeration value="ROLE_ACCESS_PUBLIC_QUEUE_PAIRING"/>
            <xs:enumeration value="ROLE_EDIT_CHECKLIST_UW"/>
            <xs:enumeration value="ROLE_SHOW_CHECKLIST_UW"/>
            <xs:enumeration value="ROLE_EDIT_CHECKLIST_HS"/>
            <xs:enumeration value="ROLE_SHOW_CHECKLIST_HS"/>
            <xs:enumeration value="ROLE_RUN_SYNCHRONIZE_DOC"/>
            <xs:enumeration value="ROLE_RUN_PROCESS_ADMIN"/>
            <xs:enumeration value="ROLE_SHOW_PROCESS_ADMIN"/>
            <xs:enumeration value="ROLE_ACCESS_CUSTOMER360VIEW"/>
            <xs:enumeration value="ROLE_DASHBOARD_BRANCH"/>
            <xs:enumeration value="ROLE_MA_BRANCH_DOCUMENTS"/>
            <xs:enumeration value="ROLE_ACCESS_PROCESS_DOC_BASIC"/>
            <xs:enumeration value="ROLE_ACCESS_PROCESS_DOC_EXTEND"/>
            <xs:enumeration value="ROLE_EDIT_CLIENT_DATA"/>
            <xs:enumeration value="ROLE_EDIT_DELIVERED_DOC"/>
            <xs:enumeration value="ROLE_ADD_DELIVERED_DOC"/>
            <xs:enumeration value="ROLE_EDIT_PROOF"/>
            <xs:enumeration value="ROLE_EDIT_DRAWN_CONDITION"/>
            <xs:enumeration value="ROLE_SET_DRAWN_CONDITION"/>
            <xs:enumeration value="ROLE_EDIT_MORTGAGE_PARAMS"/>
            <xs:enumeration value="ROLE_EDIT_MORTGAGE_TRANCHES"/>
            <xs:enumeration value="ROLE_VIEW_MORTGAGE_TRANCHES"/>
            <xs:enumeration value="ROLE_ACCESS_URGENT_COMMUNICATION"/>
            <xs:enumeration value="ROLE_CANCEL_PERSONAL_DATA_CHANGE"/>
            <xs:enumeration value="ROLE_DOCUMENTS_GC"/>
            <xs:enumeration value="ROLE_ACCESS_IDENTITY"/>
            <xs:enumeration value="ROLE_EDIT_IDENTITY"/>
            <xs:enumeration value="ROLE_IDENTIFY_PERSON"/>
            <xs:enumeration value="ROLE_EDIT_RISK_DATA"/>
            <xs:enumeration value="ROLE_ACCESS_FACE_MATCH_FINDINGS_QUEUE"/>
            <xs:enumeration value="ROLE_ACCESS_DEVICE_CONFIRMATION"/>
            <xs:enumeration value="ROLE_EDIT_LEGAL_ENTITY_EMBOSSED_NAME"/>
            <xs:enumeration value="ROLE_ACCESS_AML_PROFILE"/>
            <xs:enumeration value="ROLE_RUN_CUSTOMER_DATA_UPDATE_PROCESS"/>
        </xs:restriction>
    </xs:simpleType>

  <!-- WARNING: THE CONTENT OF THIS FILE IS GENERATED, DO NOT EDIT IT. Use AMS RightXsdWriter.java to generate its content (see pom.xml). -->

</xs:schema>