package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.campaignplannedcall.StoreCampaignPlannedCallEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.kafka.producer.StoreCampaignPlannedCallKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.StoreCampaignPlannedCallEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.CampaignCallEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import cz.airbank.sas_agent_chstream.service.Agent;
import cz.airbank.sas_agent_chstream.validator.AvroValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Map;

import static cz.airbank.sas_agent_chstream.util.CreativeContentUtil.parseCreativeContent;

@Service
@Slf4j
@RequiredArgsConstructor
public class CampaignPlannedCallAgent implements Agent {

    private final StoreCampaignPlannedCallKafkaProducer producer;
    private final TaskCache taskCache;
    private final ObjectMapper objectMapper;
    private final SasRepository sasRepository;
    private final StoreCampaignPlannedCallEventMapper mapper;

    @Override
    public AgentType getAgentType() {
        return AgentType.CAMPAIGN_PLANNED_CALL;
    }

    @Override
    public void processMessage(String event) {
        CampaignCallEvent campaignCallEvent;
        try {
            campaignCallEvent = objectMapper.readValue(event, CampaignCallEvent.class);
        } catch (Exception e) {
            log.error("Failed to parse event json into BannerEvent object", e);
            return;
        }

        String taskId = campaignCallEvent.attributes().taskId();
        String taskVersionId = campaignCallEvent.attributes().taskVersionId();

        TaskCacheEntry taskCacheEntry = taskCache.getTask(taskVersionId, taskId);
        if (taskCacheEntry == null) {
            log.warn("Failed to retrieve task cache for taskVersionId {} and taskId {}. Stop processing event", taskVersionId, taskId);
            return;
        }

        Map<String, String> creativeContent = parseCreativeContent(campaignCallEvent.attributes().creativeContent());
        String campaignName = sasRepository.getCampaignName(taskCacheEntry.tsk_comm_camp_name.value);
        String leadId = getLeadId(
                campaignCallEvent.attributes().datahubId(),
                campaignCallEvent.attributes().responseTrackingCode(),
                campaignCallEvent.attributes().timestamp());

        StoreCampaignPlannedCallEvent storeCampaignPlannedCallEvent;
        try {
            storeCampaignPlannedCallEvent = mapper.toStoreCampaignPlannedCallEvent(campaignCallEvent.attributes(), creativeContent, taskCacheEntry, leadId, campaignName);
            AvroValidator.validateNonNullableFields(storeCampaignPlannedCallEvent);
        } catch (CHStreamAgentException e) {
            sasRepository.storeErrorToDb(leadId, taskCacheEntry.tsk_comm_chan_code.value, campaignCallEvent.attributes().timestamp(), e.getMessage());
            return;
        }

        producer.publish(storeCampaignPlannedCallEvent.getCuid(), storeCampaignPlannedCallEvent);
    }

    @Override
    public void processMessage(JSONObject event) throws Exception {
        throw new UnsupportedOperationException("Use processMessage(String event)");
    }
}
