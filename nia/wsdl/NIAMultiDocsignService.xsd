<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:tns="http://airbank.cz/case/nia/docsign"
           targetNamespace="http://airbank.cz/case/nia/docsign">

    <!--
        This schema describes TOs to be sent to CASE to initiate document (object) signing authorization
    -->


    <!-- Single PDF -->
    <xs:element name="bankIdDocumentSign">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="tns:abstractSignRequest">
                    <xs:sequence>
                        <xs:element name="document" type="tns:documentForSign"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- Objects -->
    <xs:element name="bankIdSignObjectSign">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="tns:abstractSignRequest">
                    <xs:sequence>
                        <xs:element name="signObject" type="tns:objectForSign"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- Single PDF + Objects -->
    <xs:element name="bankIdDocSignObjectSign">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="tns:abstractSignRequest">
                    <xs:sequence>
                        <xs:element name="document" type="tns:documentForSign"/>
                        <xs:element name="signObject" type="tns:objectForSign"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- Multi PDF -->
    <xs:element name="bankIdEnvelopeSign">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="tns:abstractEnvelopeSignRequest">
                    <xs:sequence>
                        <xs:element name="documentObjects" type="tns:documentsForSign"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- Multi PDF + Objects -->
    <xs:element name="bankIdEnvelopeSignObjectSign">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="tns:abstractEnvelopeSignRequest">
                    <xs:sequence>
                        <xs:element name="documentObjects" type="tns:documentsForSign"/>
                        <xs:element name="signObject" type="tns:objectForSign"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- Types definitions -->
    <xs:complexType name="abstractSignRequest" abstract="true">
        <xs:sequence>
            <xs:element name="appName" type="xs:string"/>
            <xs:element name="providerName" type="xs:string"/>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="qrInitialization" type="xs:boolean"/>
            <xs:element name="knownProfile" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="abstractEnvelopeSignRequest" abstract="true">
        <xs:complexContent>
            <xs:extension base="tns:abstractSignRequest">
                <xs:sequence>
                    <xs:element name="envelopeName" type="xs:string"/>
                    <xs:element name="envelopeHash" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="objectForSign">
        <xs:sequence>
            <xs:element name="fields" type="tns:signFields"/>
            <xs:element name="hash" type="xs:string"/>
        </xs:sequence>
        <!--            <signObject>-->
        <!--                <fields>-->
        <!--                    <field key="Consent 1">I consent with sending marketing messages to my email and telephone</field>-->
        <!--                    <field key="Consent 2">I consent with sending marketing messages to my email and telephone</field>-->
        <!--                </fields>-->
        <!--                <hash>b2f50bbdeffbb3f0a34e426eee34f006fbbeebe001921eef46eeeeda3a9b27bde1d8b24b95b5db56a9ab27fa7157c0e8cb9bd7ef61d574f3c68d03eb127ff402</hash>-->
        <!--            </signObject>-->
    </xs:complexType>

    <xs:complexType name="signFields">
        <xs:sequence>
            <xs:element name="field" type="tns:signField" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="signField">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute type="xs:string" name="key"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="documentsForSign">
        <xs:sequence>
            <xs:element name="document" type="tns:documentForSign" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="documentForSign">
        <xs:sequence>
            <xs:element name="id" type="xs:string"/>
            <xs:element name="hash" type="xs:string"/>
            <xs:element name="title" type="xs:string"/>
            <xs:element name="subject" type="xs:string"/>
            <xs:element name="language" type="xs:string"/>
            <xs:element name="created" type="xs:dateTime"/>
            <xs:element name="author" type="xs:string"/>
            <xs:element name="size" type="xs:long"/>
            <xs:element name="pages" type="xs:long"/>
            <xs:element name="readByEnduser" type="xs:boolean"/>
            <xs:element name="uri" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>