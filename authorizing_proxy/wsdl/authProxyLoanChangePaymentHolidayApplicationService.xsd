<?xml version="1.0" encoding="UTF-8"?>
<xs:schema
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/authproxy/ws/loanchangepaymentholiday"
        xmlns:common="http://airbank.cz/authproxy/ws/common"
        elementFormDefault="qualified"
>

    <xs:import schemaLocation="authProxyCommon.xsd" namespace="http://airbank.cz/authproxy/ws/common" />

    <xs:element name="UpdateSummaryWrapperRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:WrapperRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryWrapperResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:WrapperResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="FetchUpdateSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:FetchRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>