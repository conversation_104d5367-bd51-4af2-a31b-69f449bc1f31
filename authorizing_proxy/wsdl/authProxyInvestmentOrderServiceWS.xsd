<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://airbank.cz/authproxy/ws/investmentOrder"
           elementFormDefault="qualified">
    <xs:import schemaLocation="../external/topas/xsd/InvestmentOrderWS.xsd" namespace="http://www.arbes.com/fis/fas/airbank/ws/order"/>
    <xs:import schemaLocation="../external/case/xsd/authorizationService.xsd" namespace="http://airbank.cz/case/ws/authorizationService"/>

    <xs:element name="FetchSetBuyOrderResultRequest">
        <xs:annotation>
            <xs:documentation>Request to fetch operation result. Can be done only after successful authorization.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="authId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ID of the authorization we want the operation result of.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="FetchSetSellOrderResultRequest">
        <xs:annotation>
            <xs:documentation>Request to fetch operation result. Can be done only after successful authorization.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="authId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ID of the authorization we want the operation result of.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>