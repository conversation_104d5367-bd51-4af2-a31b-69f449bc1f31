<?xml version="1.0" encoding="UTF-8"?>
<xs:schema
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/authproxy/ws/generalcontractapplication"
        xmlns:common="http://airbank.cz/authproxy/ws/common"
        elementFormDefault="qualified">

    <xs:import schemaLocation="authProxyCommon.xsd" namespace="http://airbank.cz/authproxy/ws/common"/>

    <xs:element name="WrapperUpdateFirstScoringApprovedRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:WrapperRequest">
                    <xs:sequence>
                        <xs:element name="cuid" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>
                                    Walkin CUID
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="WrapperUpdateFirstScoringApprovedResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:WrapperResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="FetchUpdateFirstScoringApprovedRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:FetchRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>