<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- $Id: 35e92c5a042e058c56b8fa65ef7eba03bf32ea43 $ -->
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:sch="http://homecredit.net/manhattan/mdm/ws/cz/codeList/"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	name="MDMCodeListWS" targetNamespace="http://homecredit.net/manhattan/mdm/ws/cz/codeList/">
	<wsdl:types>
		<xs:schema targetNamespace="http://homecredit.net/manhattan/mdm/ws/cz/codeList/"
			elementFormDefault="qualified">
			<xs:include schemaLocation="MDMCodeListWS.xsd" />			
		</xs:schema>
	</wsdl:types>

	<!--MESSAGEs -->

	<!--Update -->
	<!--register list message -->
	<wsdl:message name="getCodeListRequest">
		<wsdl:part name="getCodeListRequest" element="sch:getCodeListRequest" />
	</wsdl:message>
	<wsdl:message name="getCodeListResponse">
		<wsdl:part name="getCodeListResponse" element="sch:getCodeListResponse" />
	</wsdl:message>
	<!--register detail message -->
	<wsdl:message name="getCodeListDetailRequest">
		<wsdl:part name="getCodeListDetailRequest" element="sch:getCodeListDetailRequest" />
	</wsdl:message>
	<wsdl:message name="getCodeListDetailResponse">
		<wsdl:part name="getCodeListDetailResponse" element="sch:getCodeListDetailResponse" />
	</wsdl:message>
	<wsdl:message name="getFullCodeListRequest">
		<wsdl:part name="getFullCodeListRequest" element="sch:getFullCodeListRequest" />
	</wsdl:message>
	<wsdl:message name="getFullCodeListResponse">
		<wsdl:part name="getFullCodeListResponse" element="sch:getFullCodeListResponse" />
	</wsdl:message>


	<!--TYPEs -->

	<wsdl:portType name="MDMCodeListWS">
		<wsdl:documentation>Common register service - supplies different
			register's values in common structure
        </wsdl:documentation>
		<!--Register List -->
		<wsdl:operation name="getCodeList">
			<wsdl:documentation>Request for list of values. Only key and name are
				returned.
				Request: name of a desired register
				Response: List of values
				from a desired register if any
				This operation has no defined error
				codes.
            </wsdl:documentation>
			<wsdl:input name="getCodeListRequest" message="sch:getCodeListRequest" />
			<wsdl:output name="getCodeListResponse" message="sch:getCodeListResponse" />
		</wsdl:operation>
		<!--Register Detail -->
		<wsdl:operation name="getCodeListDetail">
			<wsdl:documentation>Request for all attributes of a record of given
				register identified by key.
				Request: register name and key value
				Response: full detail of a register value
				This operation has no
				defined error codes.
            </wsdl:documentation>
			<wsdl:input name="getCodeListDetailRequest" message="sch:getCodeListDetailRequest" />
			<wsdl:output name="getCodeListDetailResponse" message="sch:getCodeListDetailResponse" />
		</wsdl:operation>
		<wsdl:operation name="getFullCodeList">
			<wsdl:documentation>
				Request for full value of a register by codeLlist
				name
				Request: codelist Name
				Response: full detail
				of a register value
			</wsdl:documentation>
			<wsdl:input name="getFullCodeListRequest" message="sch:getFullCodeListRequest" />
			<wsdl:output name="getFullCodeListResponse" message="sch:getFullCodeListResponse" />
		</wsdl:operation>
	</wsdl:portType>

	<!--BINDINGs -->

	<wsdl:binding name="MDMCodeListWSSOAP" type="sch:MDMCodeListWS">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<!--Register list -->
		<wsdl:operation name="getCodeList">
			<soap:operation soapAction="" />
			<wsdl:input name="getCodeListRequest">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="getCodeListResponse">
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<!--Register Detail -->
		<wsdl:operation name="getCodeListDetail">
			<soap:operation soapAction="" />
			<wsdl:input name="getCodeListDetailRequest">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="getCodeListDetailResponse">
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getFullCodeList">
			<soap:operation soapAction="" />
			<wsdl:input name="getFullCodeListRequest">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="getFullCodeListResponse">
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>

	<!--SERVICE -->

	<wsdl:service name="MDMCodeListWS">
		<wsdl:port binding="sch:MDMCodeListWSSOAP" name="MDMCodeListWSSOAP">
			<soap:address location="http://TO-BE-SPECIFIED/mdm/" />
		</wsdl:port>
	</wsdl:service>

</wsdl:definitions>
