<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://osb.abank.cz/osb/ws/RiskData" targetNamespace="http://osb.abank.cz/osb/ws/RiskData">

        <wsdl:types>
            <xs:schema targetNamespace="http://osb.abank.cz/osb/ws/RiskData">
                <xs:include schemaLocation="RiskDataWS.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="RiskDataRequest">
            <wsdl:part element="RiskDataRequest" name="RiskDataRequest" />
        </wsdl:message>
        <wsdl:message name="RiskDataResponse">
            <wsdl:part element="RiskDataResponse" name="RiskDataResponse" />
        </wsdl:message>

        <wsdl:portType name="RiskData">
            <wsdl:operation name="RiskData">
                <xs:documentation>Poskytování dat z AB do HC</xs:documentation>
                <wsdl:input message="RiskDataRequest" />
                <wsdl:output message="RiskDataResponse" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="RiskDataBinding" type="RiskData">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="RiskData">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="RiskDataWS">
            <wsdl:port binding="RiskDataBinding" name="RiskDataPort">
                <soap:address location="/osb/ws/riskData" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
