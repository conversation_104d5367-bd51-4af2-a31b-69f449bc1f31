<?xml version="1.0" encoding="UTF-8"?>
<!--
<PERSON><PERSON><PERSON><PERSON> definující strukturu exekučního příkazu

změny:
- částka pohledávky poviná
- typy sdílené s jinými zprávami přesunuty do Common.xsd
- upravený reg. výraz pro číslo účtu – kompatibilita s frameworky PHP pro XML
- min/max – oprava typu na decimal
- pevná částka přejmenována na pravidelná částka, doplněny údaje (perioda, ...) a popis
-->

<xs:schema version="1.1"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="https://www.czech-ba.cz/eExekuce/ep"
           xmlns="https://www.czech-ba.cz/eExekuce/ep"
           elementFormDefault="qualified">

    <xs:include schemaLocation="DistraintCommon.xsd" />

    <xs:element name="elektronickyPrikaz">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identifikace" type="IdentifikaceDokumentu">
                    <xs:annotation>
                        <xs:documentation>Identifikační údaje exekučního příkazu</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="datumVydani" type="xs:date" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Datum vydání exekučního příkazu ve formátu yyyy-mm-dd, napr. 2014-04-05. Mandatorní údaj, pro zpracování nemá význam</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="obecneUdaje" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Obecne udaje exekucniho prikazu</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="zakladniMena" type="KodMeny" minOccurs="1" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>Kód měny dle ISO 4217. Mandatorní údaj, měna by měla odpovídat měně hlavní pohledávky. Další propočty jsou právě v této měně. Česká koruna: CZK</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="vystavce" type="Vystavce" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Výstavce exekučního příkazu</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="majitelUctu" type="MajitelUctu" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Majitelé a jejich účty</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="opravneny" type="ZastupitelnaOsoba" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Oprávněný – účastník (účastníci) exekučního řízení, v jehož prospěch je toto vedeno</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pohledavka" type="Pohledavka" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Pohledávka v exekuci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="SystemVypoctu">
        <xs:annotation>
            <xs:documentation>Systém výpočtu úroků</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="(U|R)\d{4}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Perioda">
        <xs:annotation>
            <xs:documentation>Perioda vyplácení pravidelné částky<br/>
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="U\d{4}"/>
        </xs:restriction>
    </xs:simpleType>

    <!--Položky exekučního příkazu-->

    <xs:complexType name="Pohledavka">
        <xs:sequence>
            <xs:element name="id" minOccurs="1" maxOccurs="1" type="xs:positiveInteger">
                <xs:annotation>
                    <xs:documentation>Jednoznačný identifikátor položky - důležité pro identifikaci v případě změny</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="castka" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Jistina, základ pro výpočet úroku, pevná částka.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mena" minOccurs="1" maxOccurs="1" type="KodMeny">
                <xs:annotation>
                    <xs:documentation>Měna pohledávky</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="typ" minOccurs="1" maxOccurs="1" type="TypPohledavky">
                <xs:annotation>
                    <xs:documentation>Určuje, o jaký typ položky jde – 4 základní typy, Jistina, Jistina s úrokem, Samostatný úrok, Opakující se pevná částka</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="poradi" minOccurs="1" maxOccurs="1" type="xs:positiveInteger">
                <xs:annotation>
                    <xs:documentation>Priorita/Pořadí – Priorita pro výpočet při placení</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="platebniUdaje" minOccurs="1" maxOccurs="1" type="PlatebniUdaje">
                <xs:annotation>
                    <xs:documentation>Platební údaje pro úhradu pohledávky</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="TypPohledavky">
        <xs:choice minOccurs="1" maxOccurs="1">
            <xs:element name="jistina" type="PrazdnyElement"/>
            <xs:element name="pravidelnaCastka" type="PravidelnaCastka"/>
            <xs:element name="jistinaVcetneUroku" type="UrocenyTyp"/>
            <xs:element name="samostatnyUrok" type="UrocenyTyp"/>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="UrocenyTyp">
        <xs:annotation>
            <xs:documentation>Výše pohledávky se pravideně navyšuje úročením. Výsledná částka se omezuje minimální a maxímální zasílanou částkou</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="systemVypoctuUroku" minOccurs="1" maxOccurs="1" type="SystemVypoctu">
                <xs:annotation>
                    <xs:documentation>Určuje systém (periodu) výpočtu úroků. Hodnoty:<br/>
                        U0001 – Denní;<br/>
                        U0007 – Týdenní;<br/>
                        U0014 – Čtrnáctidenní;<br/>
                        U0100 – Měsíční;<br/>
                        U0300 – Čtvrtletní;<br/>
                        U0600 – Pololetní;<br/>
                        U1200 – Roční;<br/>
                        R0001 – REPO - FIX;<br/>
                        R0002 – Repo - Pohyblivá;<br/>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="urokovaSazba" minOccurs="1" maxOccurs="1" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Sazba vyjádřená v procentech, v případě výpočtu dle REPO sazby pak výše odchylky od REPO sazby (Ano pouze v případě, že vyžadují úročení)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="datumOd" minOccurs="1" maxOccurs="1" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Počátek periody pro výpočet úroku</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="datumDo" minOccurs="0" maxOccurs="1" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Konec periody pro výpočet úroku; nevyplněno znamená "do splacení"</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="min" minOccurs="0" maxOccurs="1" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Minimální zasílaná částka</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max" minOccurs="0" maxOccurs="1" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Maximální zasílaná částka</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PravidelnaCastka">
        <xs:annotation>
            <xs:documentation>Výše pohledávky se pravideně navyšuje o danou pevnou částku. Výsledná částka se omezuje minimální a maxímální zasílanou částkou</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="perioda" minOccurs="1" maxOccurs="1" type="Perioda">
                <xs:annotation>
                    <xs:documentation>Perioda navyšování pohledávky. Hodnoty:<br/>
                        U0001 – Denní;<br/>
                        U0007 – Týdenní;<br/>
                        U0014 – Čtrnáctidenní;<br/>
                        U0100 – Měsíční;<br/>
                        U0300 – Čtvrtletní;<br/>
                        U0600 – Pololetní;<br/>
                        U1200 – Roční<br/>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="datumOd" minOccurs="1" maxOccurs="1" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Počátek období pro nápočet pohledávky</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="datumDo" minOccurs="0" maxOccurs="1" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Konec období pro nápočet pohledávky"</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="min" minOccurs="0" maxOccurs="1" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Minimální zasílaná částka</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max" minOccurs="0" maxOccurs="1" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Maximální zasílaná částka</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>