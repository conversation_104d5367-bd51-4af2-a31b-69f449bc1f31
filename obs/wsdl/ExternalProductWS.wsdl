<?xml version="1.0" encoding="UTF-8"?>
<definitions
    name="ExternalProductWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/ExternalProductWS/"
    targetNamespace="http://airbank.cz/obs/ws/ExternalProductWS/">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/ExternalProductWS/">
            <xsd:include schemaLocation="ExternalProductWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>

    <message name="getExternalProductRequest">
        <part name="parameters" element="tns:getExternalProductRequest" />
    </message>

    <message name="getExternalProductResponse">
        <part name="parameters" element="tns:getExternalProductResponse" />
    </message>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <portType name="ExternalProductWS">
        <operation name="GetExternalProduct">
            <documentation>
                Vrátí všechny validní External Product IDs pro daný CUID a typ produktu.

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getExternalProductRequest" />
            <output message="tns:getExternalProductResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
    </portType>

    <binding name="ExternalProductWS" type="tns:ExternalProductWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="GetExternalProduct">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="ExternalProductWS">
        <port binding="tns:ExternalProductWS" name="ExternalProductWS">
            <soap:address location="/ws/ExternalProductWS" />
        </port>
    </service>
</definitions>
