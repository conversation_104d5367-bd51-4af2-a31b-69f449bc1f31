<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    name="SmeCardManagementWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://airbank.cz/obs/ws/SmeCardManagementWS/"
    targetNamespace="http://airbank.cz/obs/ws/SmeCardManagementWS/">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/SmeCardManagementWS/">
            <xsd:include schemaLocation="SmeCardManagementWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <message name="createCardRequest">
        <part name="parameters" element="tns:createCardRequest" />
    </message>

    <message name="createCardResponse">
        <part name="parameters" element="tns:createCardResponse" />
    </message>

    <message name="createCardConfirmedRequest">
        <part name="parameters" element="tns:createCardConfirmedRequest" />
    </message>

    <message name="createCardConfirmedResponse">
        <part name="parameters" element="tns:createCardConfirmedResponse" />
    </message>

    <portType name="SmeCardManagementWS">

        <operation name="createCard">
            <documentation>
                Create a new Card for mentioned SME bank account in OBS.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / OBSAccountNumber / value : Bank account does not exist.
                CLERR_WRONG_STATUS / OBSAccountNumber / value : Bank account has improper status.
            </documentation>
            <input message="tns:createCardRequest" />
            <output message="tns:createCardResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createCardConfirmed">
            <documentation>
                Process confirmation of a new SME Card creation (in CMS) on OBS side.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / CMSCardID / value : CMS Card ID not found.
            </documentation>
            <input message="tns:createCardConfirmedRequest" />
            <output message="tns:createCardConfirmedResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

    </portType>

    <binding name="SmeCardManagementWSSOAP" type="tns:SmeCardManagementWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="createCard">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createCardConfirmed">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

    </binding>

    <service name="SmeCardManagementWS">
        <port binding="tns:SmeCardManagementWSSOAP" name="SmeCardManagementWSSOAP">
            <soap:address location="/ws/SmeCardManagementWS"/>
        </port>
    </service>

</definitions>
