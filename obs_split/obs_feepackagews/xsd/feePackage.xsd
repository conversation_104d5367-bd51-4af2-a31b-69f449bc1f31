<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://airbank.cz/obs/ws/feePackage">

    <complexType name="feePackageParamsTO">
        <sequence>
            <element name="type" type="string">
                <annotation>
                    <documentation>A package type.</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>A package name.</documentation>
                </annotation>
            </element>
            <element name="feeAmount" type="decimal">
                <annotation>
                    <documentation>The amount of the package fee.</documentation>
                </annotation>
            </element>
            <element name="feeProductCount" type="long">
                <annotation>
                    <documentation>Minimum number of active products from which a fee is paid.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="feePackageTO">
        <sequence>
            <element name="type" type="string">
                <annotation>
                    <documentation>A package type.</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>A package name.</documentation>
                </annotation>
            </element>
            <element name="active" type="boolean">
                <annotation>
                    <documentation>A flag indicating whether the client has the package turned on.</documentation>
                </annotation>
            </element>
            <element name="charged" type="boolean">
                <annotation>
                    <documentation>A flag indicating whether we charge the client a package fee.</documentation>
                </annotation>
            </element>
            <element name="feeAmount" type="decimal">
                <annotation>
                    <documentation>The amount of the package fee.</documentation>
                </annotation>
            </element>
            <element name="disabledTo" type="date" minOccurs="0">
                <annotation>
                    <documentation>Date until which the package cannot be activated.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="setFeePackageTO">
        <sequence>
            <element name="idGeneralContract" type="long">
                <annotation>
                    <documentation>OBS identification of the client's general contract.</documentation>
                </annotation>
            </element>
            <element name="type" type="string">
                <annotation>
                    <documentation>A package type.</documentation>
                </annotation>
            </element>
            <element name="active" type="boolean">
                <annotation>
                    <documentation>A flag indicating whether the client has the package turned on.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

</schema>
