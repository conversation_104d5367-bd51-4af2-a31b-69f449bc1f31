<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://airbank.cz/obs/ws/overdraft"
	xmlns="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" version="1.0"
    xmlns:tns="http://airbank.cz/obs/ws/overdraft"
    xmlns:loan="http://airbank.cz/obs/ws/loan">

    <import namespace="http://airbank.cz/obs/ws/loan" schemaLocation="LoanTO.xsd"/>

	<complexType name="OverdraftLimitParams">
		<sequence>
			<element name="minAmount" type="long">
                <annotation>
                    <documentation>minimální limit kontokorentu</documentation>
                </annotation>
            </element>
			<element name="maxAmount" type="long">
                <annotation>
                    <documentation>maximální limit kontokorentu</documentation>
                </annotation>
            </element>
			<element name="interestRate" type="decimal">
                <annotation>
                  <documentation>aktu<PERSON>ln<PERSON>ová sazba pro kontokorent</documentation>
                </annotation>
            </element>
            <element name="interestRateFuture" type="tns:OverdraftInterestRate" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>budoucí úrokové sazby</documentation>
                </annotation>
            </element>
            <element name="promoCode" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Current promo code.</documentation>
                </annotation>
            </element>
		</sequence>
	</complexType>

    <complexType name="OverdraftInterestRate">
        <annotation>
            <documentation>úroková sazba</documentation>
        </annotation>
        <sequence>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>Datum začátku platnosti nové sazby</documentation>
                </annotation>
            </element>
            <element name="validTo" type="dateTime">
                <annotation>
                    <documentation>Datum konce platnost nové sazby</documentation>
                </annotation>
            </element>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>Výše sazby</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

	<complexType name="OverdraftParams">
		<sequence>
			<element name="interestRate" type="decimal">
        <annotation>
          <documentation>aktuální úroková sazba pro kontokorent</documentation>
        </annotation>
      </element>
			<element name="annualPercentageRate" type="decimal">
        <annotation>
          <documentation>RPSN (annual percentage rate)</documentation>
        </annotation>
      </element>
			<element name="overdraftRepaymentPeriod" type="long">
        <annotation>
          <documentation>Maximální délka čerpání kontokorentu do povinného splacení (vynoření) v měsících = 12</documentation>
        </annotation>
      </element>
			<element name="minInstalmentMLSCalculate" type="decimal">
        <annotation>
          <documentation>Risková minimální splátka</documentation>
        </annotation>
      </element>
			<element name="repaymentPeriodMLSCalculate" type="long">
        <annotation>
          <documentation>Počet splátek pro nápočet riskové minimální splátky - PA repaymentPeriodRisk = 96</documentation>
        </annotation>
      </element>
		</sequence>
	</complexType>

    <complexType name="OverdraftAccount">
        <sequence>
            <element name="idOverdraftAccount" type="long">
                <annotation>
                    <documentation>id účtu pro splácení KTK (účet ke kterému byt KTK poskytnut nebo pokud je ukončen, tak technický účet nebo účet, ke kterému byl KTK převázán)</documentation>
                </annotation>
            </element>
            <element name="overdraftAccountNumber" type="long">
                <annotation>
                    <documentation>číslo účtu pro splácení KTK</documentation>
                </annotation>
            </element>
            <element name="overdraftAccountName" type="string">
                <annotation>
                    <documentation>Uživatelský název účtu pro splácení kontokorentu - pokud se nejedná o klientský účet, pak null</documentation>
                </annotation>
            </element>
            <element name="overdraftCurrency" type="string">
                <annotation>
                    <documentation>písmenný ISO kód měny účtu pro splácení KTK</documentation>
                </annotation>
            </element>
            <element name="balanceAvailable" type="decimal">
                <annotation>
                  <documentation>dostupný zůstatek účtu bez povoleného přečerpání (bez kontokorentu), využije IB pro výpočet částky, kterou má klient poslat na účet pro zrušení kontokorentu a také pro zobrazení zůstatku technického účtu pro splácení KTK</documentation>
                </annotation>
            </element>
            <element name="showInternalAccount" type="boolean">
                <annotation>
                    <documentation>pokud se jedná o technický účet pro splácení KTK (v případě ukončení BU), vrátí se true – pouze tento účet IB zobrazí jako účet pro splácení na detailu KTK</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="UtilizationSuspension">
        <sequence>
            <element name="utilizationSuspensionReasonCode" type="string">
                <annotation>
                    <documentation>MDM kód důvodu zastavení čerpání</documentation>
                </annotation>
            </element>
            <element name="utilizationSuspensionReasonFrom" type="date">
                <annotation>
                    <documentation>datum počátku důvodu zastavení čerpání</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="OverdraftDetailTO">
        <sequence>
            <element name="idOverdraft" type="long">
                <annotation>
                    <documentation>ID kontokorentu</documentation>
                </annotation>
            </element>
            <element name="overdraftNumber" type="string">
                <annotation>
                    <documentation>byznys identifikátor KTK</documentation>
                </annotation>
            </element>
            <element name="status" type="loan:LoanStatusType">
                <annotation>
                    <documentation>OBS stav kontokorentu</documentation>
                </annotation>
            </element>
            <element name="overdraftAmount" type="decimal">
                <annotation>
                    <documentation>Celková výše rámce KTK</documentation>
                </annotation>
            </element>
            <element name="currentUtilizedAmount" type="decimal">
                <annotation>
                    <documentation>Aktuálně čerpaná částka</documentation>
                </annotation>
            </element>
            <element name="currentRemainingAmount" type="decimal">
                <annotation>
                    <documentation>Částka k čerpání</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal">
                <annotation>
                    <documentation>Částka pro plné doplacení (včetně případných pohledávek/blokací na BÚ)</documentation>
                </annotation>
            </element>
            <element name="amountToRepayOverdraft" type="decimal">
                <annotation>
                    <documentation>Částka pro plné doplacení (bez případných pohledávek/blokací na BÚ)</documentation>
                </annotation>
            </element>
            <element name="creditConditionComplied" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Podmínka obratu - pokud KTK nemusí plnit podmínku obratu (minulý měsíc nebyl čerpán), pak null</documentation>
                </annotation>
            </element>
            <element name="creditConditionRequiringAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>požadovaná výše kreditního obratu pro aktuální měsíc</documentation>
                </annotation>
            </element>
            <element name="creditConditionRemainingAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>zbývající výše kreditního obratu pro splnění podmínky v aktuálním měsíci</documentation>
                </annotation>
            </element>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>vyhlašovaná úroková sazba KTK (z parametrizace úročení KTK)</documentation>
                </annotation>
            </element>
            <element name="promoInterestRate" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>promo úroková sazba KTK</documentation>
                </annotation>
            </element>
            <element name="promoValidTo" type="date" minOccurs="0">
                <annotation>
                    <documentation>Datum konce platnosti promo urokove sazby</documentation>
                </annotation>
            </element>
            <element name="annualPercentageRate" type="decimal">
                <annotation>
                    <documentation>rpsn (uloženo na entitě KTK)</documentation>
                </annotation>
            </element>
            <element name="utilizedFrom" type="date" minOccurs="0">
                <annotation>
                    <documentation>Datum začátku čerpání – pokud KTK není čerpán, pak null</documentation>
                </annotation>
            </element>
            <element name="utilizedTo" type="date" minOccurs="0">
                <annotation>
                    <documentation>Datum povinného vynoření – pokud KTK není čerpán, pak null</documentation>
                </annotation>
            </element>
            <element name="currentInterest" type="decimal">
                <annotation>
                    <documentation>Obchodní úrok akt. měsíce</documentation>
                </annotation>
            </element>
            <element name="inDebt" type="boolean">
                <annotation>
                    <documentation>příznak, zdali je KTK aktuálně po splatnosti</documentation>
                </annotation>
            </element>
            <element name="currentDebt" type="decimal">
                <annotation>
                    <documentation>Pohledavky KTK po splatnosti</documentation>
                </annotation>
            </element>
            <element name="canTerminate" type="boolean">
                <annotation>
                    <documentation>true - KTK lze ukoncit, jinak false</documentation>
                </annotation>
            </element>
            <element name="overdraftAccount" type="tns:OverdraftAccount"/>
            <element name="utilizationSuspension" type="tns:UtilizationSuspension" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>aktuálně platné důvody zastavení čerpání (IB zobrazí důvody zastavení čerpání pouze pro KTK ve stavu Aktivní/Aktivní)</documentation>
                </annotation>
            </element>
            <element name="interestFreeReserve" type="boolean">
                <annotation>
                    <documentation>true - zapnuta neúročena rezerva kontokorentu</documentation>
                </annotation>
            </element>
            <element name="interestFreeReserveAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Vyse neúročene rezervy kontokorentu</documentation>
                </annotation>
            </element>

        </sequence>
    </complexType>

    <complexType name="OverdraftSimpleDetailTO">
        <sequence>
            <element name="idOverdraft" type="long">
                <annotation>
                    <documentation>ID KTK</documentation>
                </annotation>
            </element>
            <element name="balanceWithOverdraft" type="decimal">
                <annotation>
                    <documentation>Dostupný zůstatek včetně KTK</documentation>
                </annotation>
            </element>
            <element name="overdraftAmount" type="decimal">
                <annotation>
                    <documentation>Celkový rámec KTK</documentation>
                </annotation>
            </element>
            <element name="currentUtilizedAmount" type="decimal">
                <annotation>
                    <documentation>Kolik má klient vyčerpáno z rámce KTK</documentation>
                </annotation>
            </element>
            <element name="currentRemainingAmount" type="decimal">
                <annotation>
                    <documentation>Kolik může klient ještě čerpat z KTK</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="OverdraftExtendedDetailTO">
        <sequence>
            <element name="idOverdraft" type="long">
                <annotation>
                    <documentation>Overdraft ID</documentation>
                </annotation>
            </element>
            <element name="status">
                <annotation>
                    <documentation>Status for RISK department - copy of ExtendedLoanTO.status</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="NEW"/>
                        <enumeration value="CANCELLED_BY_CLIENT"/>
                        <enumeration value="CANCELLED_BY_BANK"/>
                        <enumeration value="REJECTED"/>
                        <enumeration value="PENDING"/>
                        <enumeration value="ACTIVE"/>
                        <enumeration value="REPAID_STD"/>
                        <enumeration value="REPAID_PREMATURELY"/>
                        <enumeration value="REPAID_CONSOLIDATION"/>
                        <enumeration value="WITHDRAWN_UNPAID"/>
                        <enumeration value="WITHDRAWN_PAID"/>
                        <enumeration value="CALLED_DUE_UNPAID"/>
                        <enumeration value="CALLED_DUE_PAID"/>
                        <enumeration value="WRITE_OFF_UNPAID" />
                        <enumeration value="WRITE_OFF_PAID" />
                        <enumeration value="TO_WRITTEN_OFF_UNPAID" />
                        <enumeration value="TO_WRITTEN_OFF_PAID" />
                    </restriction>
                </simpleType>
            </element>
            <element name="productType">
                <simpleType>
                    <restriction base="string">
                        <enumeration value="OVERDRAFT">
                            <annotation>
                                <documentation>Overdraft</documentation>
                            </annotation>
                        </enumeration>
                    </restriction>
                </simpleType>
            </element>
            <element name="idApplication" type="long">
                <annotation>
                    <documentation>Overdraft application id</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long">
                <annotation>
                    <documentation>envelope id</documentation>
                </annotation>
            </element>
            <element name="approvalDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Overdraft approval date</documentation>
                </annotation>
            </element>
            <element name="closeDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Overdraft closing date</documentation>
                </annotation>
            </element>
            <element name="plannedDueDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Overdraft due date</documentation>
                </annotation>
            </element>
            <element name="overdraftAmount" type="decimal">
                <annotation>
                    <documentation>Overdraft limit amount</documentation>
                </annotation>
            </element>
            <element name="currentUtilizedAmount" type="decimal">
                <annotation>
                    <documentation>Overdraft actual utilized amount</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal">
                <annotation>
                    <documentation>Amount needed to completely repay this overdraft</documentation>
                </annotation>
            </element>
            <element name="actualDPDWithTolerance" type="int" minOccurs="0">
                <annotation>
                    <documentation>Days past due (taking into account DPD tolerance)</documentation>
                </annotation>
            </element>
            <element name="actualDPD" type="int" minOccurs="0">
                <annotation>
                    <documentation>Days past due (NOT taking into account DPD tolerance)</documentation>
                </annotation>
            </element>
            <element name="actualInternalDPDWithTolerance" type="int" minOccurs="0">
                <annotation>
                    <documentation>Internal days past due (taking into account DPD tolerance)</documentation>
                </annotation>
            </element>
            <element name="actualInternalDPD" type="int" minOccurs="0">
                <annotation>
                    <documentation>Internal days past due (NOT taking into account DPD tolerance)</documentation>
                </annotation>
            </element>
            <element name="creditConditionComplied" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Credit condition compliance. NULL in case credit condition is NOT tracked (overdraft not used last month)</documentation>
                </annotation>
            </element>
            <element name="creditConditionRemainingAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Remaining amount to be credited to current account in order to comply with credit condition in current month</documentation>
                </annotation>
            </element>
            <element name="utilizationSuspended" type="boolean">
                <annotation>
                    <documentation>Overdraft utilization suspended flag</documentation>
                </annotation>
            </element>
            <element name="pastDueAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Overdraft past due amount</documentation>
                </annotation>
            </element>
            <element name="dateLastUtilization" type="date" minOccurs="0">
                <annotation>
                    <documentation>Date of last overdraft utilization</documentation>
                </annotation>
            </element>
            <element name="overdraftNumber" type="string">
                <annotation>
                    <documentation>Overdraft number</documentation>
                </annotation>
            </element>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>Overdraft interest rate</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="SuspensionDetail">
        <sequence>
            <element name="reason" type="string">
                <annotation>
                    <documentation>Reason why overdraft utilization was suspended. Code list of reasons is maitained by MDM</documentation>
                </annotation>
            </element>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>Overdraft utilization suspended from</documentation>
                </annotation>
            </element>
            <element name="validTo" type="dateTime">
                <annotation>
                    <documentation>Overdraft utilization suspended to</documentation>
                </annotation>
            </element>
            <element name="note" type="string" minOccurs="0">
                <annotation>
                    <documentation>Optional remark</documentation>
                </annotation>
            </element>
            <element name="createdBy" type="string">
                <annotation>
                    <documentation>User (system) who created this record</documentation>
                </annotation>
            </element>
            <element name="modifiedBy" type="string" minOccurs="0">
                <annotation>
                    <documentation>User/s (system/s) who modified this record</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="TimeIntervalType">
        <restriction base="string">
            <enumeration value="DAILY">
                <annotation>
                    <documentation>denní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WEEKLY">
                <annotation>
                    <documentation>týdenní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MONTHLY">
                <annotation>
                    <documentation>měsíční</documentation>
                </annotation>
            </enumeration>
            <enumeration value="YEARLY">
                <annotation>
                    <documentation>roční</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

</schema>
