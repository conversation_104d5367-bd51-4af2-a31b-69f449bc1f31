<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/OBSUserNameWS" xmlns:c="http://airbank.cz/obs/ws/obsUserName" xmlns="http://www.w3.org/2001/XMLSchema">
	<import namespace="http://airbank.cz/obs/ws/obsUserName" schemaLocation="../xsd/OBSUserName.xsd"/>

    <element name="CheckUserNameRequest">
        <complexType>
            <sequence>
                <element name="userName" type="string">
                    <annotation>
                        <documentation>
                            přihlašovací uživatelské username
                        </documentation>
                    </annotation>
                </element>
                <element name="cuid" type="long" minOccurs="0">
                    <annotation>
                        <documentation>Tato metoda vyžaduje CUID pro kontrolu, zda uživatel
                            nebyl v minulosti klientem.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="CheckUserNameResponse">
        <complexType>
            <sequence>
                <element name="allowed" type="boolean">
                    <annotation>
                        <documentation>
                            true - username uživatel může použít
                            false - nelze použít
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="FindUserNameRequest">
        <complexType>
            <sequence>
                <element name="userName" type="string" minOccurs="0">
                    <annotation>
                        <documentation>
                            přihlašovací uživatelské username
                        </documentation>
                    </annotation>
                </element>
                <element name="cuid" type="long" minOccurs="0">
                    <annotation>
                        <documentation>
                            identifikace klienta
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="FindUserNameResponse">
        <complexType>
            <sequence>
                <element name="userNameDetail" type="c:UserNameDetail" minOccurs="0">
                    <annotation>
                        <documentation>
                            Obsahuje status, userName, cuid, informace o blokaci a possessionAuthenticationRequested
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="LockUserNameRequest">
        <complexType>
            <sequence>
                <element name="CUID" type="long">
                    <annotation>
                        <documentation>
                            ID klienta ze systému CIF
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="LockUserNameResponse">
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
</schema>
