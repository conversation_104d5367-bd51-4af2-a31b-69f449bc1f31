<?xml version="1.0" encoding="UTF-8"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:ntf="http://airbank.cz/obs/ws/notification"
    xmlns:ctr="http://airbank.cz/obs/ws/contract"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    elementFormDefault="qualified" version="1.0">


    <include schemaLocation="BinDocument.xsd" />
    <include schemaLocation="AutentizationAuthorization.xsd" />
    <import schemaLocation="Notification.xsd" namespace="http://airbank.cz/obs/ws/notification" />
    <import schemaLocation="contract.xsd" namespace="http://airbank.cz/obs/ws/contract" />

    <simpleType name="RelationType">
        <restriction base="string">
            <enumeration value="ACTIVE_CONTRACT" />
            <enumeration value="CARD_HOLDER" />
            <enumeration value="DISPONENT" />
            <enumeration value="CODEBTOR" />
            <enumeration value="ACTIVE_ENTITLED" />
            <enumeration value="CONTRACT_PROPOSAL" />
            <enumeration value="CARD_HOLDER_PROPOSAL" />
            <enumeration value="DISPONENT_PROPOSAL" />
            <enumeration value="ENTITLED_PROPOSAL" />
            <enumeration value="CONTRACT_HISTORIC" />
            <enumeration value="CARD_HOLDER_HISTORIC" />
            <enumeration value="DISPONENT_HISTORIC" />
            <enumeration value="CODEBTOR_HISTORIC" />
            <enumeration value="ENTITLED_HISTORIC" />
        </restriction>
    </simpleType>

    <complexType name="ContractTO">
        <annotation>
            <documentation>smlouva - buď RS nebo dodatek</documentation>
        </annotation>
        <sequence>
            <element name="idContract" type="long">
                <annotation>
                    <documentation>
                        identifikace smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID envelope</documentation>
                </annotation>
            </element>
            <element name="contractStatus" type="tns:ContractStatus">
                <annotation>
                    <documentation>stav smlouvy</documentation>
                </annotation>
            </element>
            <element name="contractType" type="tns:ContractType">
                <annotation>
                    <documentation>
                        kód typu smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="idApplication" type="long" minOccurs="0">
                <annotation>
                    <documentation>Application identification</documentation>
                </annotation>
            </element>
            <element name="deactivationDate" type="date" minOccurs="0" />
            <element name="deactivationReason" type="string" minOccurs="0" />
            <element name="signature" type="ctr:ContractSignature" minOccurs="0" />
            <element name="lastStatusChange" type="dateTime">
                <annotation>
                    <documentation>
                        poslední datum změny stavu smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="binDocumentIdent" type="com:BinDocumentIdentType" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        binární perzonifikovaný dokumenty (pdf, html a
                        podobně) navázané na dodatek nebo RS
                    </documentation>
                </annotation>
            </element>
            <element name="persDocStatus" type="tns:PersDocStatusType">
                <annotation>
                    <documentation>
                        stav perzonalizovaného dokumentu
                    </documentation>
                </annotation>
            </element>
            <element name="persDocDistChannelSent" type="tns:PersDocDistChannelSentType">
                <annotation>
                    <documentation>
                        kanál odeslání perzonalizovaného dokumentu
                    </documentation>
                </annotation>
            </element>
            <element name="persDocSearchCode" type="string">
                <annotation>
                    <documentation>vyhledávací kód perzonalizovaného dokumentu</documentation>
                </annotation>
            </element>
            <element name="automaticCompletion" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        jestli se zpracovavalo automatickou kompletaci
                    </documentation>
                </annotation>
            </element>
            <element name="completionWorkflowID" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        LAP request ID v pripade ze se nejedna o prilepeny pers. dokument
                    </documentation>
                </annotation>
            </element>
            <element name="AppendixNumber" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                      Poradove cislo dodatku. RS ma 0, dalsi dodatky jsou vzdy +1.
                      Nektere typy kompletaci to nemaji vyplneno, napr.:
                      - cestovni pojisteni,
                      - DPS,
                      - kompletace pro podpis jinou osobou:
                        - Prohlaseni disponenta, drzitele, nebo dokumenty pro spoludluznika u hypotek
                      - ...
                    </documentation>
                </annotation>
            </element>
            <element name="generalContractType" type="tns:GeneralContractType" minOccurs="0">
                <annotation>
                    <documentation>Typ rámcové smlouvy</documentation>
                </annotation>
            </element>

        </sequence>
    </complexType>

    <simpleType name="ContractStatus">
        <restriction base="string">
            <enumeration value="UNSIGNED" />
            <enumeration value="SIGNED" />
            <enumeration value="ACTIVE" />
            <enumeration value="TERMINATED" />
            <enumeration value="REJECTED" />
            <enumeration value="CANCELLED_BY_BANK" />
            <enumeration value="CANCELLED_BY_CLIENT" />
            <enumeration value="DOCUMENTED">
                <annotation>
                    <documentation>
                        doloženo SJM nebo Žádost o předčasné splacení půjčky - je-li navázaný Předmět kompletace
                        ve stavu Zkompletovaný
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="TO_DELIVERY">
                <annotation>
                    <documentation>k dodání (SJM nebo Žádost o předčasné splacení půjčky) - je-li navázaný Předmět kompletace ve stavu Ke kompletaci a Manuální
                        verifikace (bez
                        ohledu na stav Personalizovaného dokumentu)
                    </documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="GeneralContractTO">
        <annotation>
            <documentation>Rámcová smlouva</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>
                        id rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="contractNumber" type="string">
                <annotation>
                    <documentation>
                        číslo rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="completionId" type="long">
                <annotation>
                    <documentation>
                        ID kompletace rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="isOwner" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        zda je v roli vlastníka, jenom pokud na vstupu byl cuid
                    </documentation>
                </annotation>
            </element>
            <element name="ownerFirstName" type="string">
                <annotation>
                    <documentation>
                        jméno vlastníka rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="ownerSurname" type="string">
                <annotation>
                    <documentation>
                        příjmení vlastníka rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="ownerCuid" type="long"/>
            <element name="status">
                <annotation>
                    <documentation>
                        status rámcové smlouvy
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="ACTIVE" />
                        <enumeration value="PASIVE" />
                        <enumeration value="TERMINATED" />
                    </restriction>
                </simpleType>
            </element>
            <element name="idGeneralContract" type="long">
                <annotation>
                    <documentation>
                        DEPRECATED!!! Use completionId
                        TOTO NENÍ id rámcové smlouvy!!!

                        ID kompletace rámcové smlouvy
                    </documentation>
                </annotation>
            </element>
            <element name="idProfile" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        identifikace profilu - použije se pro security
                        kontext
                        Vyplneno jenom pokud na vstupu je cuid.
                    </documentation>
                </annotation>
            </element>
            <element name="relationType" type="tns:RelationToContractType" minOccurs="0">
                <annotation>
                    <documentation>Vztah ke smlouvě. Vyplneno jenom pokud na vstupu je cuid.</documentation>
                </annotation>
            </element>
            <element name="generalContractType" type="tns:GeneralContractType">
                <annotation>
                    <documentation>Typ rámcové smlouvy</documentation>
                </annotation>
            </element>
            <element name="dateActivated" type="date" minOccurs="0">
                <annotation>
                    <documentation>
                        datum aktivace
                    </documentation>
                </annotation>
            </element>
            <element name="isAutomatCompletedContract" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        Automaticky zkompletováno
                    </documentation>
                </annotation>
            </element>
            <element name="isActivatedOnBranch" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        Informace, zda je doručená rámcová smlouva aktivována na pobočce
                    </documentation>
                </annotation>
            </element>
            <element name="contractSubmitWay" type="tns:PersDocDistChannelSentType" minOccurs="0"/>
            <element name="contractSignType" type="tns:ContractSignType" minOccurs="0"/>
            <element name="processedSummarySubmitWay" type="tns:PersDocDistChannelSentType" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <simpleType name="ContractSignType">
        <annotation>
            <documentation>Typ podpisu</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SIGN_INK">
                <annotation>
                    <documentation>
                        Fyzický podpis - modrý podpis
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SIGNPAD">
                <annotation>
                    <documentation>
                        Podpis pomocí Signpadu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SCANNED_SIGN">
                <annotation>
                    <documentation>Naskenovaný podpis</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH_OTP">
                <annotation>
                    <documentation>Podpis SMS kódem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH_PWD">
                <annotation>
                    <documentation>Podpis zadáním hesla</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH_SWT">
                <annotation>
                    <documentation>Podpis pushkou na pobočce</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ContractType">
        <restriction base="string">
            <enumeration value="RS">
                <annotation>
                    <documentation>
                        Rámcová smlouva fyzické osoby nepodnikatele (FON, Retail)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="AUTH_RESET">
                <annotation>
                    <documentation>
                        Reset autorizačních prvků
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRICE_CLIENT">
                <annotation>
                    <documentation>
                        Změna cenového programu na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRICE_BANK">
                <annotation>
                    <documentation>
                        Změna cenového programu Bankou
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_BU">
                <annotation>
                    <documentation>Založení produktu BU</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_BU_CLIENT">
                <annotation>
                    <documentation>
                        Zrušení produktu BU na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_BU_BANK">
                <annotation>
                    <documentation>
                        Zrušení BU Bankou - bude?
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CANCEL_BU">
                <annotation>
                    <documentation>
                        Zrušení uctu SME
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_HYSA">
                <annotation>
                    <documentation>
                        Založení produktu HYSA
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HYSA_CLIENT">
                <annotation>
                    <documentation>
                        Zrušení produktu HYSA na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HYSA_BANK">
                <annotation>
                    <documentation>
                        Zrušení produktu HYSA bankou
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CANCEL_HYSA">
                <annotation>
                    <documentation>
                        Zrušení produktu HYSA pro SME
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="AVOIDANCE_HYSA_BANK">
                <annotation>
                    <documentation>Zrušení HYSA Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXED_DEPOSIT">
                <annotation>
                    <documentation>Založení terminovaneho vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATE_FIXED_DEPOSIT_BANK">
                <annotation>
                    <documentation>
                        Ukončení termínovaného vkladu z popudu banky
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATE_FIXED_DEPOSIT_CLIENT">
                <annotation>
                    <documentation>
                        Ukončení termínovaného vkladu na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK">
                <annotation>
                    <documentation>Založení produktu DK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK_INFO">
                <annotation>
                    <documentation>
                        Založení produktu DK na vědomí
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK_HOLDER">
                <annotation>
                    <documentation>
                        Založení produktu DK pro jiného Držitele
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_CHILD_DK_HOLDER">
                <annotation>
                    <documentation>
                        Založení produktu DK pro detskeho Držitele
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DECLARATE_HOLDER">
                <annotation>
                    <documentation>Prohlášení Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DECLARATE_CHILD_HOLDER">
                <annotation>
                    <documentation>Prohlášení detskeho držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DK_CLIENT">
                <annotation>
                    <documentation>
                        Zrušení  DK na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DK_INFO">
                <annotation>
                    <documentation>Zrušení DK na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADD_DISP">
                <annotation>
                    <documentation>
                        Zřízení disponenta k bankovním službám
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADD_CHILD_DISP">
                <annotation>
                    <documentation>
                        Zřízení detskeho disponenta k bankovním službám
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANNOU_DISP">
                <annotation>
                    <documentation>Prohlášení Disponenta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANNOU_CHILD_DISP">
                <annotation>
                    <documentation>Prohlášení detskeho Disponenta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_OWNER">
                <annotation>
                    <documentation>
                        Zrušení disponenta majitelem
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_DISP">
                <annotation>
                    <documentation>
                        Zrušení disponenta disponentem
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_BANK">
                <annotation>
                    <documentation>
                        Zrušení disponenta Bankou
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_OWNER">
                <annotation>
                    <documentation>
                        Zrušení držitele majitelem
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_HOLDER">
                <annotation>
                    <documentation>
                        Zrušení držitele držitelem
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_BANK">
                <annotation>
                    <documentation>
                        Zrušení držitele Bankou
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_VK">
                <annotation>
                    <documentation>Založení produktu VK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_VK_CLIENT">
                <annotation>
                    <documentation>
                        Zrušení produktu VK na žádost klienta
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_VK_BANK">
                <annotation>
                    <documentation>Zrušení VK Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_VK_CLIENT">
                <annotation>
                    <documentation>Zrušení VK klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_MOBILITY">
                <annotation>
                    <documentation>mobilita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SJM">
                <annotation>
                    <documentation>společné jmění manželů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_LOAN">
                <annotation>
                    <documentation>žádost o hotovostní úvěr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_LOAN">
                <annotation>
                    <documentation>Odstoupení od hotovostního úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PARTIAL_REPAYMENT">
                <annotation>
                    <documentation>žádost o částečné splacení úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EARLY_REPAYMENT">
                <annotation>
                    <documentation>žádost o předčasné splacení úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_REFINANCING">
                <annotation>
                    <documentation>Refinancovani uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_ACC_CLIENT">
                <annotation>
                    <documentation>odstoupení od účtu klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_LOAN_CLIENT">
                <annotation>
                    <documentation>odstoupení od úvěru klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANGE_INST_DATE">
                <annotation>
                    <documentation>zmena parametru data splatky uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANGE_INST_AMOUNT">
                <annotation>
                    <documentation>zmena parametru vyse splatky uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SET_LOAN_PAYMENT_HOLIDAY">
                <annotation>
                    <documentation>splatkove prazdniny</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RENEWAL_DK">
                <annotation>
                    <documentation>Automatická obnova debetní karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INS_TR_CREATE">
                <annotation>
                    <documentation>Založení cestovního pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE_ENROLMENT">
                <annotation>
                    <documentation>Přihlášení k pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANGE_REPAYMENT_ACC">
                <annotation>
                    <documentation>Změna účtu pro splácení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MANUAL_SUPPLEMENT">
                <annotation>
                    <documentation>Obecný dodatek ke smlouvě</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LOAN_COMMITMENT">
                <annotation>
                    <documentation>Úvěrový příslib</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LOAN_AGREEMENT">
                <annotation>
                    <documentation>Úvěrová smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_ACCESSN_TO_DEBT">
                <annotation>
                    <documentation>Přistoupení k dluhu spoludlužníkem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_MORTGAGE_DEED">
                <annotation>
                    <documentation>Zástavní smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LAND_REG_INSERT">
                <annotation>
                    <documentation>Návrh na vklad na katastr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD HyNew</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD HyNew</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMDM_INF">
                <annotation>
                    <documentation>Dodatku úvěrové smlouvy HD RefinHypo na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_CH_INST_AMOUNT">
                <annotation>
                    <documentation>Změna délky a výšky měsíční splátky HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_CH_INST_DATE">
                <annotation>
                    <documentation>Změna data splátky HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_CH_REPAY_DATE">
                <annotation>
                    <documentation>Změna data doplacení HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_EXTRAINSTALL">
                <annotation>
                    <documentation>Vklad do chytré rezervy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_ADD_DISBURSMNT">
                <annotation>
                    <documentation>Výběr z chytré rezervy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_OTB_TO_PRINCIPAL">
                <annotation>
                    <documentation>Převod chytré rezervy do jistiny</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_AGREEMENT">
                <annotation>
                    <documentation>Storno/zamitnuti uverove smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_COMMITMENT">
                <annotation>
                    <documentation>Storno/zamitnuti uveroveho prislibu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_VERIFY_CODEBTOR">
                <annotation>
                    <documentation>Overeni spoludluznika</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHYREQQUANTIFY">
                <annotation>
                    <documentation>Žádost o vyčíslení původního závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHYANNOTHELIEN">
                <annotation>
                    <documentation>Oznámení o vzniku zástavního práva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE_CANCEL">
                <annotation>
                    <documentation>Ukončení pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE_CHANGE">
                <annotation>
                    <documentation>Změna pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVL_INSR_ENROLMENT">
                <annotation>
                    <documentation>Založení cestovního pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRVL_INSR_CANCEL">
                <annotation>
                    <documentation>Zrušení cestovního pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYDRAWINGAPP" />
            <enumeration value="HY_REFIX_INFO_DEBTOR">
                <annotation>
                    <documentation>Refixace – dodatek na vědomí HD</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HY_REFIX_INFO_CODEBTOR">
                <annotation>
                    <documentation>Refixace – dodatek na vědomí SD</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_OVERDRAFT">
                <annotation>
                    <documentation>Vytvoreni KTK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERM_OVERDRAFT_CLI">
                <annotation>
                    <documentation>Ukonceni KTK klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERM_OVERDRAFT_BANK">
                <annotation>
                    <documentation>Ukonceni KTK bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_OVERDRAFT_CLI">
                <annotation>
                    <documentation>Odstoupeni KTK klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_OVERDRAFT_BNK">
                <annotation>
                    <documentation>Odstoupeni KTK bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVEST_CONTR_BANK_TERMINATION">
                <annotation>
                    <documentation>Výpověď Komisionářské smlouvy bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVEST_CONTR_BANK_WITHDRAW">
                <annotation>
                    <documentation>Odstoupení od Komisionářské smlouvy bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_TO_AB">
                <annotation>
                    <documentation>Investice do Air Bank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_CERTIFICATE">
                <annotation>
                    <documentation>Investiční certifikát</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_CERTIFICATE_CONFIRMATION">
                <annotation>
                    <documentation>Potvrzení o investičním certifikátu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_INVESTMENT_CONTRACT">
                <annotation>
                    <documentation>Žádost o Investiční smlouvu - Podílové fondy (Komisionářská smlouva)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSION_MEETING_RECORD">
                <annotation>
                    <documentation>Záznam z jednání o DPS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_PENSION_CONTRACT">
                <annotation>
                    <documentation>Uzavření doplňkového penzijního spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PP_INSURANCE_CREATE">
                <annotation>
                    <documentation>Uzavření pojištění schopnosti splácet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PP_INSURANCE_CANCEL">
                <annotation>
                    <documentation>Zrušení pojištění schopnosti plácet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PIP_INSURANCE_CREATE">
                <annotation>
                    <documentation>Uzavření pojištění osobnich veci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PIP_INSURANCE_CANCEL">
                <annotation>
                    <documentation>Zrušení pojištění osobnich veci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPI_CREATION_CODEBTOR">
                <annotation>
                    <documentation>Dodatek k přistoupení - založení PPI (SD)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REFIN_NOTICE_LETTER">
                <annotation>
                    <documentation>Konsolidace - výpovědní dopis</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_CHRU">
                <annotation>
                    <documentation>Založení produktu CHRU</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_CHILD_SAVING_ACCOUNT">
                <annotation>
                    <documentation>založení dětského spořicího účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PERSONAL_DATA_UPDATE">
                <annotation>
                    <documentation>Aktualizace osobních údajů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_GC">
                <annotation>
                    <documentation>
                        Rámcová smlouva podnikatele (FOP, SME)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CHANGE_ENTITLED">
                <annotation>
                    <documentation>Výměna oprávněné osoby k rámcové smlouvě právnické osoby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_ENTITLED">
                <annotation>
                    <documentation>
                        Zřízení Oprávněné osoby k Rámcové smlouvě podnikatele (SME Entitled person)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_BUFOP">
                <annotation>
                    <documentation>Založení produktu BUFOP (Podnikatelský účet)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_SUFOP">
                <annotation>
                    <documentation>Založení produktu SUFOP (Podnikatelský spořicí účet)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_DEBIT_CARD">
                <annotation>
                    <documentation>Založení produktu DEBIT_CARD (Vydání platební karty podnikatele)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_SPLIT_PAYMENT">
                <annotation>
                    <documentation>zalozeni rozlozeni platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_STOCK_INVESTMENT_CONTRACT">
                <annotation>
                    <documentation>Žádost o Investiční smlouvu - Akcie</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCK_INVESTMENT_CONTRACT_TERMINATION_BANK">
                <annotation>
                    <documentation>Výpověď Smlouvy o investování s Air Bank (Akcie a ETF) bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCK_INVESTMENT_CONTRACT_TERMINATION_CLIENT">
                <annotation>
                    <documentation>Výpověď Smlouvy o investování s Air Bank (Akcie a ETF) klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCK_INVESTMENT_CONTRACT_WITHDRAW_BANK">
                <annotation>
                    <documentation>Odstoupení od Smlouvy o investování s Air Bank (Akcie a ETF) bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCK_INVESTMENT_CONTRACT_WITHDRAW_CLIENT">
                <annotation>
                    <documentation>Odstoupení od Smlouvy o investování s Air</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_W_8_BEN_CONTRACT">
                <annotation>
                    <documentation>
                        Formulář W8BEN (W-8BEN je dokument amerického daňového úřadu, který slouží pro zamezení dvojího zdanění z výnosu cenných papírů amerických společností nebo společností nakoupených na amerických trzích - NYSE, NASDAQ.)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="PIP_INSURANCE_CREATE">
                <annotation>
                    <documentation>Uzavření pojištění věcí a peněz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PIP_INSURANCE_CANCEL">
                <annotation>
                    <documentation>Zrušení pojištění věcí a peněz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_BUPO">
                <annotation>
                    <documentation>Založení podnikatelského účtu PO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_CREATE_SUPO">
                <annotation>
                    <documentation>Založení podnikatelského spořicího účtu PO</documentation>
                </annotation>
            </enumeration>

            <!-- New values in alphabetical order -->
        </restriction>
    </simpleType>

    <complexType name="PersonRelationToContract">
        <sequence>
            <element name="cuid" type="long" />
            <element name="relationType" type="tns:RelationType" />
            <element name="allowedSazkaByOwner" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>zda má daný uživatel povolené generovat Sazka kód
                        true - má vololeno

                        Atribut je použit pouze pro disponenty a disponenty v návrh.
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="RelationToContractType">
        <restriction base="string">
            <enumeration value="OWNER">
                <annotation>
                    <documentation>Vlastník</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARD_HOLDER">
                <annotation>
                    <documentation>Držitel karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISPONENT">
                <annotation>
                    <documentation>Disponent</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTITLED">
                <annotation>
                    <documentation>Oprávněná osoba</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="ContractIdent">
        <sequence>
            <element name="idContract" type="long">
                <annotation>
                    <documentation>primární klíč smlouvy/dodatku</documentation>
                </annotation>
            </element>
            <element name="idApplication" type="long">
                <annotation>
                    <documentation>primární klíč žádosti z IB</documentation>
                </annotation>
            </element>
            <element name="completionType" type="tns:CompletionTypeTO">
                <annotation>
                    <documentation>typ kompletace</documentation>
                </annotation>
            </element>
            <element name="expirationDate" type="dateTime">
                <annotation>
                    <documentation>datum expirace kompletace</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LimitTO">
        <sequence>
            <element name="value" type="decimal" />
            <element name="maxValue" type="decimal" />
            <element name="remainingValue" type="decimal" />
            <element name="limitChannel" type="tns:LimitChannelType" />
        </sequence>
    </complexType>

    <simpleType name="LimitChannelType">
        <restriction base="string">
            <enumeration value="IB" />
            <enumeration value="ICC" />
            <enumeration value="BRANCH" />
            <enumeration value="SPB" />
            <enumeration value="OPENAPI" />
        </restriction>
    </simpleType>

    <complexType name="UnpersonDocumentTO">
        <sequence>
            <element name="type" type="tns:UnpersonDocumentType">
                <annotation>
                    <documentation>typ dokumentu</documentation>
                </annotation>
            </element>
            <element name="validFrom" type="date">
                <annotation>
                    <documentation>právní platnost od</documentation>
                </annotation>
            </element>
            <element name="validTo" type="date">
                <annotation>
                    <documentation>právní platnost do</documentation>
                </annotation>
            </element>
            <element name="binDocumentIdent" type="com:BinDocumentIdentType">
                <annotation>
                    <documentation>
                        id binárního dokumentu
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="UnpersonDocumentType">
        <restriction base="string">
            <enumeration value="VOP">
                <annotation>
                    <documentation>všeobecné obchodní podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PL">
                <annotation>
                    <documentation>ceník</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASAPRDCOND">
                <annotation>
                    <documentation>kartové produktové podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRDCOND">
                <annotation>
                    <documentation>produktové podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCECONDIT">
                <annotation>
                    <documentation>pojistné podmínky BPI</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCECONDIT_PIP">
                <annotation>
                    <documentation>pojistné podmínky PIP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCECONDIT_PZP">
                <annotation>
                    <documentation>pojistné podmínky PZP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INTERESVIEW">
                <annotation>
                    <documentation>úrokové sazby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOANCOND">
                <annotation>
                    <documentation>Produktové (úvěrové) podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGECOND">
                <annotation>
                    <documentation>hypotecni produktové podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEDQ1">
                <annotation>
                    <documentation>Lékařský dotazník - úmrtí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEDQ2">
                <annotation>
                    <documentation>Lékařský dotazník - invalidita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEDQ3">
                <annotation>
                    <documentation>Lékařský dotazník - pracovní neschopnost</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEEMPCONF">
                <annotation>
                    <documentation>Potvrzení zaměstnavatele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FPVINFO">
                <annotation>
                    <documentation>Informační memorandum o Pojištění vkladů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCEPL">
                <annotation>
                    <documentation>Přehled cen pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRAVINSCOND">
                <annotation>
                    <documentation>Pojistné podmínky cestovního pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXAMPLECONTRACT">
                <annotation>
                    <documentation>Vzorová RS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFTCOND">
                <annotation>
                    <documentation>Podmínky pro používání Kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENTSCOND">
                <annotation>
                    <documentation>Investiční produktové podmínky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENTSPL">
                <annotation>
                    <documentation>Ceník investičních služeb</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SECURITYRULES">
                <annotation>
                    <documentation>Zásady bezpečnosti</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISCOUNTPROGRAM">
                <annotation>
                    <documentation>Podmínky poskytování odměn a výhod</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FID">
                <annotation>
                    <documentation>Sdělení informací o poplatcích</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNIFORMSERVICEDSGN">
                <annotation>
                    <documentation>Jednotné označení služeb</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONSTATUTE">
                <annotation>
                    <documentation>Statuty účastnických fondů k doplňkovému penzijnímu spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONKEYINFO">
                <annotation>
                    <documentation>Klíčové informace k účastnickým fondům doplňkového penzijního spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONTERMSANDCONDS">
                <annotation>
                    <documentation>Smluvní podmínky doplňkového penzijního spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONPRECONTRACTINFO">
                <annotation>
                    <documentation>Předsmluvní informace k doplňkovému penzijnímu spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONPRICELIST">
                <annotation>
                    <documentation>Sazebník doplňkového penzijního spoření a informace o pobídkách</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSIONGDPR">
                <annotation>
                    <documentation>Poučení o ochraně osobních údajů DPS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPILOAN">
                <annotation>
                    <documentation>Pojistné podmínky – Pojištění půjčky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPIMORTGAGE">
                <annotation>
                    <documentation>Pojistné podmínky – Pojištění hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_EXAMPLECONTRACT">
                <annotation>
                    <documentation>SME Vzorová smluvní dokumentace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_PL">
                <annotation>
                    <documentation>SME Ceník</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_VOP">
                <annotation>
                    <documentation>SME VOP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_INTERESVIEW">
                <annotation>
                    <documentation>SME Přehled úrokových sazeb</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENTS_STATEMENTS_SPEC">
                <annotation>
                    <documentation>Technické požadavky a specifikace pro import hromadných plateb a export výpisů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCKETFCOND">
                <annotation>
                    <documentation>Obchodní podmínky k investování</documentation>
                </annotation>
            </enumeration>

            <!-- New values in aplhabetical order -->
            <enumeration value="ADDITIONAL_SERVICES_COND">
                <annotation>
                    <documentation>Podmínky doplňkových služeb</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXED_DEPOSIT_COND">
                <annotation>
                    <documentation>Podmínky pro používání termínovaného vkladu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_CERT_COND">
                <annotation>
                    <documentation>Podmínky k Investici do Air Bank (IC)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_CERT_PRECONTRACTINFO">
                <annotation>
                    <documentation>Předsmluvní informace k Investici do Air Bank (IC)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_TO_AB_COND">
                <annotation>
                    <documentation>Podmínky k Investici do Air Bank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_TO_AB_PRECONTRACTINFO">
                <annotation>
                    <documentation>Předsmluvní informace k Investici do Air Bank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENT_COND">
                <annotation>
                    <documentation>Podmínky platebního styku</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="BillManagerCommChannelType">
        <restriction base="string">
            <enumeration value="IBWO">
                <annotation>
                    <documentation>
                        kanál Internet Banking a ICC
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="IBWO_EMAIL">
                <annotation>
                    <documentation>
                        kanál Internet Banking, ICC a email
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="IBWO_SMS">
                <annotation>
                    <documentation>
                        kanál Internet Banking, ICC a SMS
                    </documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PersDocStatusType">
        <annotation>
            <documentation>stavy perzonalizovaných dokumentů</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="TOSEND">
                <annotation>
                    <documentation>K odeslání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SENT">
                <annotation>
                    <documentation>Odeslaný</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCELED">
                <annotation>
                    <documentation>Stornovaný</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNDELIVEREDTOBANK">
                <annotation>
                    <documentation>Nedoručený do banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNDELIVEREDTOCLIENT">
                <annotation>
                    <documentation>Nedoručený klientovi</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RECEIVED">
                <annotation>
                    <documentation>Přijatý</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PROCESSED">
                <annotation>
                    <documentation>Zpracovany</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLETED">
                <annotation>
                    <documentation>Ve spisovne</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTCOMPLETED">
                <annotation>
                    <documentation>v externi spisovne</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BORROWED">
                <annotation>
                    <documentation>zapujceny</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INPREPARATION">
                <annotation>
                    <documentation>V přípravě</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PersDocDistChannelSentType">
        <annotation>
            <documentation>kanál odeslání</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IB">
                <annotation>
                    <documentation>IB</documentation>
                </annotation>
            </enumeration>
            <enumeration value="POST">
                <annotation>
                    <documentation>Pošta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MESSENGER">
                <annotation>
                    <documentation>Kurýr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH">
                <annotation>
                    <documentation>Pobočka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EMAIL">
                <annotation>
                    <documentation>Jiný email</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EMAILPR">
                <annotation>
                    <documentation>Primární email Majitele RS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EMAILDP">
                <annotation>
                    <documentation>Primární email Disponenta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EMAILCH">
                <annotation>
                    <documentation>Primární email Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPB">
                <annotation>
                    <documentation>Mobilní aplikace</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="SignChannelType">
        <annotation>
            <documentation>channel of signature</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IB">
                <annotation>
                    <documentation>internet banking</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TB">
                <annotation>
                    <documentation>telephone banking</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH">
                <annotation>
                    <documentation>branch</documentation>
                </annotation>
            </enumeration>
            <enumeration value="POST">
                <annotation>
                    <documentation>post</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MESSENGER">
                <annotation>
                    <documentation>courier</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPB">
                <annotation>
                    <documentation>smart phone banking</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ProductVariant">
        <annotation>
            <documentation>seznam produktova varianta</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CARD">
                <annotation>
                    <documentation>Plastová karta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STICKER">
                <annotation>
                    <documentation>Nálepka</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ProductType">
        <annotation>
            <documentation>výčet produktů</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="LOAN">
                <annotation>
                    <documentation>úvěr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVING_ACCOUNT">
                <annotation>
                    <documentation>spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHILD_SAVING_ACCOUNT">
                <annotation>
                    <documentation>dětský spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEBIT_CARD">
                <annotation>
                    <documentation>debetní karta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>běžný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_MOBILITY">
                <annotation>
                    <documentation>mobilita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_LOAN_PAYDAY">
                <annotation>
                    <documentation>změna data splátky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_LOAN_INST_AMOUNT">
                <annotation>
                    <documentation>změna výše splátky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_LOAN_INST_COL">
                <annotation>
                    <documentation>změna výše splátky managerem vymahani</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SET_LOAN_PAYMENT_HOLIDAY">
                <annotation>
                    <documentation>nastaveni splatkovych prazdnin na pujcce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRA_INSTALMENT">
                <annotation>
                    <documentation>mimořádná splátka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAY_SOONER">
                <annotation>
                    <documentation>Předčasné splacení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_REFINANCING">
                <annotation>
                    <documentation>Refinancovani uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CONSOLIDATION">
                <annotation>
                    <documentation>Konsolidace uveru - pro sluzbu getAvailableLoanBin</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE_LOAN_EXTRA_INSTALMENT">
                <annotation>
                    <documentation>extra instalment on mortgage loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_MORTGAGE_LOAN_PAYDAY">
                <annotation>
                    <documentation>change of instalment day on mortgage loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_MORTGAGE_LOAN_INST_PREPAY_AMOUNT">
                <annotation>
                    <documentation>change of instalment or (and) prepay amount on mortgage loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHNG_MORTGAGE_LOAN_INST_COL">
                <annotation>
                    <documentation>change of instalment amount on mortgage loan by collateral manager</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTB_PAYBACK">
                <annotation>
                    <documentation>payback from open to buy account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE_LOAN">
                <annotation>
                    <documentation>mortgage loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE">
                <annotation>
                    <documentation>insurance</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INS_TRAVEL">
                <annotation>
                    <documentation>travel insurance</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT">
                <annotation>
                    <documentation>kontokorent</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATE_OVERDRAFT">
                <annotation>
                    <documentation>ukonceni kontokorentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSION">
                <annotation>
                    <documentation>doplnkove penzijni sporeni</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RENTIER">
                <annotation>
                    <documentation>Zonky Rentiér</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPI_TO_ACTIVE_LOAN">
                <annotation>
                    <documentation>pojisteni schopnosti splacet pro aktivni uver</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PORTU">
                <annotation>
                    <documentation>Portu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPLITPAYMENT">
                <annotation>
                    <documentation>rozlozeni platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTREPRENEUR_CURRENT_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTREPRENEUR_SAVING_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LEGAL_ENTITY_CURRENT_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LEGAL_ENTITY_SAVING_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SME_DEBIT_CARD">
                <annotation>
                    <documentation>debetní karta pro podnikatele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STOCK_ETF">
                <annotation>
                    <documentation>Individuální investování (akcie a ETF)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="W_8_BEN">
                <annotation>
                    <documentation>Formulář W-8BEN (dokument amerického daňového úřadu, který slouží pro zamezení dvojího zdanění z výnosů)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PIP_INSURANCE">
                <annotation>
                    <documentation>Pojištění věcí a peněz</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ReasonRejectionType">
        <restriction base="string">
            <enumeration value="NUMBER_OF_LOAN">
                <annotation>
                    <documentation>
                        překročen max. počet úvěrů
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DPD_LOAN">
                <annotation>
                    <documentation>
                        překročet maximální počet dní po splatnosti na
                        úvěrech
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DPD_DEPOSIT">
                <annotation>
                    <documentation>
                        překročet maximální počet dní po splatnosti na
                        depozitech
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DPD_OVERDRAFT">
                <annotation>
                    <documentation>
                        překročet maximální počet dní po splatnosti na KTK
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEBIT_AMOUNT">
                <annotation>
                    <documentation>
                        překročena celková dlužná částka (dohromady za
                        úvěry i deposita)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_SUPPLEMENT">
                <annotation>
                    <documentation>
                        klient má nezkompletovaný dodatek o hotovostním
                        úvěru
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_CARDSFORPERSON">
                <annotation>
                    <documentation>
                        klient překročil maximální počet karet pro osobu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_CARDSFORCONTRACT">
                <annotation>
                    <documentation>
                        klient překročil maximální počet karet pro smlouvu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_STICKERSFORPERSON">
                <annotation>
                    <documentation>
                        klient překročil maximální počet nálepek pro osobu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_STICKERSFORCONTRACT">
                <annotation>
                    <documentation>
                        klient překročil maximální počet nálepek pro smlouvu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="NO_BU">
                <annotation>
                    <documentation>
                        klient nemá žádný bežný účet
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_BU">
                <annotation>
                    <documentation>
                        překročen limit maximálního počtu bežných účtů
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_SU">
                <annotation>
                    <documentation>
                        překročen limit maximálního počtu spořících účtů
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_OWNER">
                <annotation>
                    <documentation>OBS zkontroluje (pro Žádost o BÚ, SÚ i HÚ), zda subjekt má platnou vazbu typu Vlastník na Smlouvu ve stavu:
                        - V návrhu (může zažádat, ale v IB nemůže podepsat) a současně je Smlouva navázaná na PK typu RS ve stavu Zkompletovaný
                        - nebo Účinná.
                        Pokud ne, pak OBS vrátí informaci, že klient nemůže žádat - Subjekt není vlastníkem účinné/zkompletované Smlouvy. - spira-17727
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_WITHDRAWN">
                <annotation>
                    <documentation>
                        Úvěr ve stavu : Odstoupený (včetně odstoupený v zákonné lhůtě)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_CALLING_DUE">
                <annotation>
                    <documentation>
                        Úvěr ve stavu : Zesplatněný
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_WRITTEN_OFF">
                <annotation>
                    <documentation>
                        Úvěr ve stavu : Odepsaný
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_TO_WRITE_OFF">
                <annotation>
                    <documentation>
                        Úvěr ve stavu : K odpisu
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_TERMINATED">
                <annotation>
                    <documentation>
                        Úvěr ve stavu : Ukončený
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT_DELIQUENCY">
                <annotation>
                    <documentation>
                        Klient v delikvenci
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSTALMENT_MISSED">
                <annotation>
                    <documentation>
                        Nezaplacená splátka v den pravidelné splátky
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_FINAL_TERM">
                <annotation>
                    <documentation>
                        Kontrola, zda je klient v posledním měsíci splácení a zároveň má nastaven maximální dobu splácení podle produktové varianty
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_MAX_MODIF">
                <annotation>
                    <documentation>
                        Překročen povolený počet změn
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_OPT_LOCK">
                <annotation>
                    <documentation>
                        Změna splátkáče (optimistický zámek)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_LOCKED">
                <annotation>
                    <documentation>
                        Klient nema moznost změnit parametry / jsou platne pouze ty aktuální
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN_LAP_PARAM">
                <annotation>
                    <documentation>
                        Pro dane limity neexistují nastavitelné parmetry úvěru (LAP vrátí chybné limity)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="REF_LOAN_BRKI">
                <annotation>
                    <documentation>Nesmí být před datem reportování do BRKI, kontrola vůči číselníku společností : tolerance v měsících (parametr)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_CAMPAIGN_CODE">
                <annotation>
                    <documentation>kampan nenalezena (pro pripad chybneho marketingoveho kodu)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ACCOUNT_EXISTS">
                <annotation>
                    <documentation>Ucet klienta jiz existuje / jen pro sluzbu setObligationRepaymentDetails</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVALID_BANK_CODE">
                <annotation>
                    <documentation>Kod banky nenalezen v ciselniku bank / jen pro sluzbu setObligationRepaymentDetails</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_AMOUNT_MAX">
                <annotation>
                    <documentation>Částka k doplacení musí být v produktových variantách pro ref / con : prekročenie maximálnej čiastky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_AMOUNT_MIN">
                <annotation>
                    <documentation>Částka k doplacení musí být v produktových variantách pro ref / con : prekročenie minimálnej čiastky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_LOAN_FAKE">
                <annotation>
                    <documentation>Kontrola na nesmyslně zadané hodnoty původního úvěru (výše půjčky, výše splátky, doba splácení, datum načerpání) -  RPSN max, min</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_TERM_MAX">
                <annotation>
                    <documentation>Kontrola na dĺžku splácania povodneho uveru. Počet splátok je nad povoleným limitom.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_TERM_MIN">
                <annotation>
                    <documentation>Kontrola na dĺžku splácania povodneho uveru. Počet splátok je pod povoleným limitom.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_LOAN_REPAID">
                <annotation>
                    <documentation>Kontrola na dĺžku splácania povodneho uveru. Úver je už splatený.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TECHNICAL_ERROR">
                <annotation>
                    <documentation>Technická chyba v OBS : spadne SQL procedura</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FUTURE_INST_CHANGE">
                <annotation>
                    <documentation>
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_LOANBIN_MAX">
                <annotation>
                    <documentation>Částka k doplacení je vyšší než LoanBin schválený pro KONS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTB_ZERO">
                <annotation>
                    <documentation>OTB = 0</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT_EXECUTION">
                <annotation>
                    <documentation>Klient je v exekuci nebo insolvenci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT_FRAUD">
                <annotation>
                    <documentation>Klient je v podezření na fraud</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT_DEATH">
                <annotation>
                    <documentation>Klient je v podezření na úmrtí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE_DEBT">
                <annotation>
                    <documentation>Klient má dluh na pojistném</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RS_NOTCOMPLETED">
                <annotation>
                    <documentation>Subjekt nemá zkompletovanou RS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="JPK_EXISTS">
                <annotation>
                    <documentation>Produkt nelze založit, klient má JPK účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOW_RISK_LB">
                <annotation>
                  <documentation>Klient žádá o další HU, přitom nemá uhrazenou žádnou splátku a celkové čerpání překračuje risk-loanbin</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_EXISTS">
                <annotation>
                  <documentation>Klient žádá o KTK, přitom už jeden neukončený má</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_NOT_EXISTS">
                <annotation>
                  <documentation>Klient žádá o zrušení KTK, který nebyl nalezen</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_DRAWN">
                <annotation>
                  <documentation>Klient žádá o zrušení KTK, ale aktuálně ho čerpá</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_PENALTY_DEBT">
                <annotation>
                  <documentation>Klient žádá o zrušení KTK, ale nemá dost prostředků na urhazení úroků z KTK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_NO_ACCOUNT">
                <annotation>
                  <documentation>Klient nemá účet, pro který lze založit KTK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT_CARD_HOLD">
                <annotation>
                  <documentation>Klient žádá o zrušení KTK, ale nemá dost prostředků na úhradu karetních blokací</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_OBLAMOUNT_MIN">
                <annotation>
                    <documentation>Podlimitní závazek pro konsolidaci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_OBLAMOUNT_MAX">
                <annotation>
                    <documentation>Nadlimitní závazek pro konsolidaci</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_OVERCREDLIMIT">
                <annotation>
                    <documentation>Částka k doplacení u kreditní karty/kontokorentu je vyšší než rámec (včetně započtení možného přečerpání)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON_LOAN_NORESAMOUNT">
                <annotation>
                    <documentation>Nebyla vyplněna hodnota residualAmount.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENSION_EXISTS">
                <annotation>
                    <documentation>Klient žádá o doplňkové penzijní spoření, přitom má již existující doplňkové penzijní spoření.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENT_HOLIDAY_USED_THIS_YEAR">
                <annotation>
                    <documentation>Splátkové prázdniny lze využít pouze jednou v kalendářním roce.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIRST_INSTALMENT_NOT_PAID">
                <annotation>
                    <documentation>První splátka ještě nebyla uhrazena.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_ENOUGH_FUTURE_INSTALMENTS">
                <annotation>
                    <documentation>Požadavek nelze splnit, úvěr je téměř doplacen.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NEXT_INSTALMENT_TOO_SOON">
                <annotation>
                    <documentation>Blíží se datum splátky.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLOSE_OF_BUSINESS">
                <annotation>
                    <documentation>Probíhá závěrka.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NO_INSTALMENT_PAID_BETWEEN_PAYMENT_HOLIDAYS">
                <annotation>
                    <documentation>Nezaplacená žádná řádná splátka mezi splátkovými prázdninami.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENT_HOLIDAY_SET">
                <annotation>
                    <documentation>Na této půjčce jsou sjednány splátkové prázdniny.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAXPERDAY">
                <annotation>
                    <documentation>Překročení povoleného počtu splátek za den.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLERR_MONTHLY_FINAL">
                <annotation>
                    <documentation>Probíhá uzávěrka.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INCORRECT_TRANSACTION_STATUS">
                <annotation>
                    <documentation>Transakce neexistuje nebo není aktivní.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_ALLOWED_FOR_SPLITPAYMENT">
                <annotation>
                    <documentation>PPI neni povoleno pro rozlozeni platby.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TOPUP_FIRST_INSTALMENT_NOT_PAID">
                <annotation>
                    <documentation>První splátka na navyšované konsolidaci ještě nebyla uhrazena.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="DebtTO">
        <sequence>
            <element name="name" type="string" minOccurs="0">
                <annotation>
                    <documentation>název úvěru/účtu</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>výše dluhu</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>kód měny</documentation>
                </annotation>
            </element>
            <element name="deptType" type="tns:DeptType">
                <annotation>
                    <documentation>
                        z jakého produktu je daný dluh
                    </documentation>
                </annotation>
            </element>
            <element name="idProduct" type="long">
                <annotation>
                    <documentation>identifikace úvěru (idLoan) nebo SU/BU (idBankAccount)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ExternalProductDebtTO">
        <sequence>
            <element name="name" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        PAYMENT_PROTECTION_INSURANCE - název úvěru, ke kteréhu se dluh váže
                        BILL_PROTECTION_INSURANCE - null
                    </documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>výše dluhu</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>kód měny</documentation>
                </annotation>
            </element>
            <element name="deptType" type="tns:ExternalProductDebtType">
                <annotation>
                    <documentation>z jakého externího produktu je daný dluh</documentation>
                </annotation>
            </element>
            <element name="idProduct" type="long">
                <annotation>
                    <documentation>identifikace externího produktu</documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>číslo účtu, na který se dluh hradí</documentation>
                </annotation>
            </element>
            <element name="accountName" type="string">
                <annotation>
                    <documentation>název účtu, na který se dluh hradí</documentation>
                </annotation>
            </element>
            <element name="idLoan" type="long">
                <annotation>
                    <documentation>identifikace uveru pokud je dluh externiho produktu svazan s uverem(typicky pojisteni PPI), jinak je prazdny</documentation>
                </annotation>
            </element>
            <element name="extProductId" type="string">
                <annotation>
                    <documentation>identifikace produktu u externí společnosti</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanDebtTO">
        <complexContent>
            <extension base="tns:DebtTO">
                <sequence>
                    <element name="loanAmount" type="decimal">
                        <annotation>
                            <documentation>výše úvěru</documentation>
                        </annotation>
                    </element>
                    <element name="loanCurrency" type="string">
                        <annotation>
                            <documentation>kód měny výše úvěru</documentation>
                        </annotation>
                    </element>
                    <element name="loanAccount" type="tns:LoanAccountTO">
                        <annotation>
                            <documentation>information about loan account</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanAccountTO">
        <annotation>
            <documentation>information about loan account</documentation>
        </annotation>
        <sequence>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>number of loan account</documentation>
                </annotation>
            </element>
            <element name="userBankAccountName" type="string" minOccurs="0">
                <annotation>
                    <documentation>user name of loan account</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="DeptType">
        <annotation>
            <documentation>
                z jakého produktu je daný dluh
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SAVING_ACCOUNT">
                <annotation>
                    <documentation>spořící účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOAN">
                <annotation>
                    <documentation>úvěr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE_LOAN">
                <annotation>
                    <documentation>hypotéka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>běžný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="JPK_ACCOUNT">
                <annotation>
                    <documentation>jpk účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT">
                <annotation>
                    <documentation>kontokorent</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ExternalProductDebtType">
        <annotation>
            <documentation>z jakého externího produktu je daný dluh</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="BILL_PROTECTION_INSURANCE">
                <annotation>
                    <documentation>pojištění výdajů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAYMENT_PROTECTION_INSURANCE">
                <annotation>
                    <documentation>pojištění schopnosti splácet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PERSONAL_ITEMS_PROTECTION">
                <annotation>
                    <documentation>pojištění věcí a peněz</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="PermissionSettingsTO">
        <annotation>
            <documentation>current access rights for RS</documentation>
        </annotation>
        <sequence>
            <element name="generalPermission">
                <annotation>
                    <documentation>general access - access to all entities of specific type</documentation>
                </annotation>
                <complexType>
                    <sequence>
                        <element name="loansAllowed" type="boolean">
                            <annotation>
                                <documentation>view on all loans (regardless of the accessibility of payment account)</documentation>
                            </annotation>
                        </element>
                        <element name="accountsAllowed" type="boolean">
                            <annotation>
                                <documentation>access to all accounts (also future)</documentation>
                            </annotation>
                        </element>
                        <element name="documentOrganizerAllowed" type="boolean">
                            <annotation>
                                <documentation>access to document organizer</documentation>
                            </annotation>
                        </element>
                    </sequence>
                </complexType>
            </element>
            <element name="accountPermission">
                <annotation>
                    <documentation>
                        access to the accounts; is filled even if access is granted to all accounts
                    </documentation>
                </annotation>
                <complexType>
                    <sequence>
                        <element name="idBankAccount" type="long" minOccurs="0" maxOccurs="unbounded">
                            <annotation>
                                <documentation>primary key of bank account</documentation>
                            </annotation>
                        </element>
                    </sequence>
                </complexType>
            </element>
        </sequence>
    </complexType>

    <simpleType name="AnyPermission">
        <annotation>
            <documentation>user permission</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ANY_LOAN">
                <annotation>
                    <documentation>user has permission to all loans and has at least one loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANY_ACCOUNT">
                <annotation>
                    <documentation>user has permission to all accounts and has at least one account or has permission to specific accounts</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANY_SAZKA">
                <annotation>
                    <documentation>user has permission to sazka withdrawal and has at least one current account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOCUMENT_ORGANIZER">
                <annotation>
                    <documentation>user has permission to document organizer</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="CardActivatinAllowance">
        <restriction base="string">
            <enumeration value="ALLOWED" />
            <enumeration value="NOT_ALLOWED_COMPLETION_NOT_FINISHED" />
            <enumeration value="NOT_ALLOWED_DOCUMENTS_MISSING" />
        </restriction>
    </simpleType>

    <simpleType name="ActivateAccountsResult">
        <restriction base="string">
            <enumeration value="OK">
                <annotation>
                    <documentation>aktivace proběhla v pořádku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOK_DOCUMENT_STATUS_REL">
                <annotation>
                    <documentation>RS není v návrhu nebo uživatel není její vlastník</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOK_DOCUMENT_NOT_SIGNED">
                <annotation>
                    <documentation>RS není podepsána</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOK_PASSIVE_BANKACCOUNT_NOT_FOUND">
                <annotation>
                    <documentation>účet nenalezen</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="CreateContractType">
        <annotation>
            <documentation>comletion type enumeration</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="RHY_LOAN_AGREEMENT">
                <annotation>
                    <documentation>Úvěrová smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_ACCESSN_TO_DEBT">
                <annotation>
                    <documentation>Přistoupení k dluhu spoludlužníkem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_MORTGAGE_DEED">
                <annotation>
                    <documentation>Zástavní smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LAND_REG_INSERT">
                <annotation>
                    <documentation>Návrh na vklad na katastr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMDM_INF">
                <annotation>
                    <documentation>Dodatku úvěrové smlouvy HD RefinHypo na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_COMMITMENT">
                <annotation>
                    <documentation>Odstoupení od úvěrového příslibu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_AGREEMENT">
                <annotation>
                    <documentation>Odstoupení od úvěrové smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD HyNew</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD HyNew</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="CreateMortgageContractDocTO">
        <sequence>
            <element name="contractType" type="tns:CreateContractType">
                <annotation>
                    <documentation>contract type</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>user identification by CIF id</documentation>
                </annotation>
            </element>
            <element name="loanNumber" type="string">
                <annotation>
                    <documentation>loan number</documentation>
                </annotation>
            </element>
            <element name="applicationId" type="long">
                <annotation>
                    <documentation>identification of application about HYREF</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long">
                <annotation>
                    <documentation>identification of application envelope</documentation>
                </annotation>
            </element>
            <element name="fileId" type="string" minOccurs="0">
                <annotation>
                    <documentation>identification file by DMS id</documentation>
                </annotation>
            </element>
            <element name="distributionChannel" type="tns:PersDocDistChannelSentType">
                <annotation>
                    <documentation>distribution channel of contract document</documentation>
                </annotation>
            </element>
            <element name="numberOfCopies" type="int" minOccurs="0">
                <annotation>
                    <documentation>number of copies</documentation>
                </annotation>
            </element>
            <element name="documentVariant" type="string" minOccurs="0">
                <annotation>
                    <documentation>Variant of contract document</documentation>
                </annotation>
            </element>
            <element name="publish" type="boolean">
                <annotation>
                    <documentation>Publish contract document?</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CreateMortgageContractDocResTO">
        <sequence>
            <element name="contractType" type="tns:CreateContractType">
                <annotation>
                    <documentation>contract type</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>user identification by CIF id</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long">
                <annotation>
                    <documentation>identification of application envelope</documentation>
                </annotation>
            </element>
            <element name="contractId" type="long">
                <annotation>
                    <documentation>identifier of created contract</documentation>
                </annotation>
            </element>
            <element name="fileId" type="string" minOccurs="0">
                <annotation>
                    <documentation>identification file by DMS id</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="CompletionTypeTO">
        <restriction base="string">
            <enumeration value="RS" />
            <enumeration value="CREATE_BU" />
            <enumeration value="CREATE_HYSA" />
            <enumeration value="CREATE_CHILD_SAVING_ACCOUNT" />
            <enumeration value="CREATE_DK" />
            <enumeration value="CREATE_DK_INFO" />
            <enumeration value="CREATE_DK_HOLDER" />
            <enumeration value="CREATE_CHILD_DK_HOLDER" />
            <enumeration value="CREATE_VK" />
            <enumeration value="CREATE_LOAN" />
            <enumeration value="CANCEL_LOAN" />
            <enumeration value="DECLARATE_HOLDER" />
            <enumeration value="DECLARATE_CHILD_HOLDER" />
            <enumeration value="ADD_DISP" />
            <enumeration value="ADD_CHILD_DISP" />
            <enumeration value="ANNOU_DISP" />
            <enumeration value="ANNOU_CHILD_DISP" />
            <enumeration value="CANCEL_DK_INFO" />
            <enumeration value="CANCEL_DK_CLIENT" />
            <enumeration value="CANCEL_BU_BANK" />
            <enumeration value="CANCEL_BU_CLIENT" />
            <enumeration value="SME_CANCEL_BU" />
            <enumeration value="CANCEL_VK_BANK" />
            <enumeration value="CANCEL_VK_CLIENT" />
            <enumeration value="CANCEL_HYSA_CLIENT" />
            <enumeration value="CANCEL_HYSA_BANK" />
            <enumeration value="SME_CANCEL_HYSA" />
            <enumeration value="CANCEL_DISP_BANK" />
            <enumeration value="CANCEL_DISP_DISP" />
            <enumeration value="CANCEL_DISP_OWNER" />
            <enumeration value="RESIGN_ACC_CLIENT" />
            <enumeration value="RESIGN_LOAN_CLIENT" />
            <enumeration value="PRICE_BANK" />
            <enumeration value="PRICE_CLIENT" />
            <enumeration value="CREATE_MOBILITY" />
            <enumeration value="PARTIAL_REPAYMENT" />
            <enumeration value="EARLY_REPAYMENT" />
            <enumeration value="CHANGE_INST_DATE" />
            <enumeration value="CHANGE_INST_AMOUNT" />
            <enumeration value="CREATE_REFINANCING" />
            <enumeration value="RENEWAL_DK" />
            <enumeration value="AUTH_RESET" />
            <enumeration value="HYPO_EXTRAINSTALL" />
            <enumeration value="HYPO_ADD_DISBURSMNT" />
            <enumeration value="HYPO_CH_INST_DATE" />
            <enumeration value="HYPO_CH_REPAY_DATE" />
            <enumeration value="HYPO_CH_INST_AMOUNT" />
            <enumeration value="RHY_LOAN_COMMITMENT" />
            <enumeration value="RHY_LOAN_AGREEMENT" />
            <enumeration value="RHY_ACCESSN_TO_DEBT" />
            <enumeration value="RHY_MORTGAGE_DEED" />
            <enumeration value="RHY_LAND_REG_INSERT" />
            <enumeration value="RHY_DEBTOR_AMENDMNT" />
            <enumeration value="RHY_DEBTOR_AMDM_INF" />
            <enumeration value="RHY_CODEBT_AMENDMNT" />
            <enumeration value="RHY_CNCL_AGREEMENT" />
            <enumeration value="RHY_CNCL_COMMITMENT" />
            <enumeration value="RHY_VERIFY_CODEBTOR" />
            <enumeration value="INSURANCE_ENROLMENT" />
            <enumeration value="CHANGE_REPAYMENT_ACC" />
            <enumeration value="MANUAL_SUPPLEMENT" />
            <enumeration value="INSURANCE_CANCEL" />
            <enumeration value="INSURANCE_CHANGE" />
            <enumeration value="TRVL_INSR_ENROLMENT" />
            <enumeration value="TRVL_INSR_CANCEL" />
            <enumeration value="CREATE_OVERDRAFT" />
            <enumeration value="CREATE_INVESTMENT_CONTRACT" />
            <enumeration value="PP_INSURANCE_CREATE" />
            <enumeration value="PP_INSURANCE_CANCEL" />
            <enumeration value="REFIN_NOTICE_LETTER" />
            <enumeration value="PPI_CREATION_CODEBTOR" />
            <enumeration value="SME_GC" />
            <enumeration value="SME_CHANGE_ENTITLED" />
            <enumeration value="SME_CREATE_ENTITLED" />
            <enumeration value="SME_CREATE_BUFOP" />
            <enumeration value="SME_CREATE_SUFOP" />
            <enumeration value="SME_CREATE_DEBIT_CARD" />
            <enumeration value="CREATE_SPLIT_PAYMENT" />
            <enumeration value="CREATE_STOCK_INVESTMENT_CONTRACT" />
            <enumeration value="STOCK_INVESTMENT_CONTRACT_TERMINATION_BANK" />
            <enumeration value="STOCK_INVESTMENT_CONTRACT_TERMINATION_CLIENT" />
            <enumeration value="STOCK_INVESTMENT_CONTRACT_WITHDRAW_BANK" />
            <enumeration value="STOCK_INVESTMENT_CONTRACT_WITHDRAW_CLIENT" />
            <enumeration value="CREATE_W_8_BEN_CONTRACT" />
            <enumeration value="SME_CREATE_BUPO" />
            <enumeration value="SME_CREATE_SUPO" />
        </restriction>
    </simpleType>

    <complexType name="NoncontractualPersDocumentTO">
        <sequence>
            <element name="type" type="tns:NoncontractualPersDocType">
                <annotation>
                    <documentation>typ dokumentu</documentation>
                </annotation>
            </element>
            <element name="dateCreated" type="date">
                <annotation>
                    <documentation>datum vzniku dokumentu</documentation>
                </annotation>
            </element>
            <element name="binDocumentIdent" type="com:BinDocumentIdentType" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        id binárního dokumentu
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="CompletionStateTO">
        <restriction base="string">
            <enumeration value="APPROVED" />
            <enumeration value="REJECTED" />
            <enumeration value="CLIENT_CANCEL" />
            <enumeration value="FI_CANCEL" />
            <enumeration value="COMPLETION" />
            <enumeration value="SUSPENDED"/>
        </restriction>
    </simpleType>

    <complexType name="ContractStateChangeTO">
        <sequence>
            <element name="completionId" type="long" />
            <element name="completionStateTO" type="tns:CompletionStateTO" />
            <element name="rejectReasonClient" type="string" minOccurs="0" />
            <element name="rejectReason" type="string" minOccurs="0" />
            <element name="automaticCompletion" type="boolean" minOccurs="0"/>
        </sequence>
    </complexType>


    <simpleType name="NoncontractualPersDocType">
        <restriction base="string">
            <enumeration value="DOT_APPLICATIONRSCANCEL">
                <annotation>
                    <documentation>Žádost o zrušení rámcové smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_EU_STAY_PERMIT">
                <annotation>
                    <documentation>EU průkaz o povolení k pobytu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RSCANCEL">
                <annotation>
                    <documentation>Zamítnutí žádosti (o RS) [BF317]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INFORMCANCEL_CA">
                <annotation>
                    <documentation>Oznámení o zrušení účtu - BÚ [BF379]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INFORMCANCEL_SA">
                <annotation>
                    <documentation>Oznámení o zrušení účtu - SÚ [BF158]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_NOTICEVOP">
                <annotation>
                    <documentation>Oznameni o vypovedi RS (klient nesouhlasí se změnou VOP) [BF162]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETP">
                <annotation>
                    <documentation>Průvodní dopis - Pokyny pro dokončení aktivace účtu - Pošta [BF301]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETM">
                <annotation>
                    <documentation>Průvodní dopis - Pokyny pro dokončení aktivace účtu - Kurýr [BF321]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETAPX">
                <annotation>
                    <documentation>Průvodní dopis k Dodatku - poštou [BF430]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETD">
                <annotation>
                    <documentation>Průvodní dopis k Prohlášení Disponenta/Držitele [BF429]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETDEL">
                <annotation>
                    <documentation>Průvodní dopis - zrušení RS [BF459]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_REQCHANGPERSDATA">
                <annotation>
                    <documentation>Žádost klienta o změnu osobních údajů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_IDENTITYCARD">
                <annotation>
                    <documentation>Občanský průkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PASSPORT">
                <annotation>
                    <documentation>Cestovní pas</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BIRTHCERTIF">
                <annotation>
                    <documentation>Rodný list</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_DRIVERLICENCE">
                <annotation>
                    <documentation>Řidičský průkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_GUNLICENCE">
                <annotation>
                    <documentation>Zbrojní průkaz</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_NATIDENTITYCARD">
                <annotation>
                    <documentation>Národní průkaz totožnosti ( u cizinců)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RESIDECEPERM">
                <annotation>
                    <documentation>Průkaz o povolení k pobytu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LISTCA">
                <annotation>
                    <documentation>Výpis z BÚ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COPYCONCA">
                <annotation>
                    <documentation>Kopie smlouvy o zřízení BÚ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_DEATHCERTIF">
                <annotation>
                    <documentation>Úmrtní list</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_REQFOREXEC">
                <annotation>
                    <documentation>Žádost o provedení exekuce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_EXECREAL">
                <annotation>
                    <documentation>Exekuce – nabytí PM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LOGININHERIT">
                <annotation>
                    <documentation>Přihlášení do dědického řízení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RESULTOFINHERIT">
                <annotation>
                    <documentation>Výsledek dědického řízení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TERMACCCONCL">
                <annotation>
                    <documentation>Výpověď účtu/smlouvy – klient</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OUTMOBILITY">
                <annotation>
                    <documentation>Mobilita odchozí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_SCANCONTRDOCBR">
                <annotation>
                    <documentation>Scan smluvní dokumentace z Pobočky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_EXPRESPEPREQUEST">
                <annotation>
                    <documentation>Vyjádření k žádosti PEP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TERMRSBYBANK">
                <annotation>
                    <documentation>Výpověď RS bankou [BF160]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RESIGNRSBYBANK">
                <annotation>
                    <documentation>Odstoupeni od RS bankou [BF161]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHER">
                <annotation>
                    <documentation>Jiný doklad</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PERMADDRESSDCL">
                <annotation>
                    <documentation>Prohlášení o trvalé adrese</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CHANGELIST">
                <annotation>
                    <documentation>Změnový lístek</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_IDENTITYCARDCONF">
                <annotation>
                    <documentation>Potvrzení o OP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PREAGREEMENTFORM">
                <annotation>
                    <documentation>Předsmluvní formulář [BF032]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LOANREPAYMENT">
                <annotation>
                    <documentation>Potvrzení o celkovém doplacení půjčky [BF048]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_SJM">
                <annotation>
                    <documentation>Společné jmění manželů [BF052]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LEASE">
                <annotation>
                    <documentation>Nájemní smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_UTILITYBILL">
                <annotation>
                    <documentation>Doklad o vyúčtování energií</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHADDRESSVERIF">
                <annotation>
                    <documentation>Jiný doklad pro ověření adresy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TELCOBILL">
                <annotation>
                    <documentation>Vyúčtování za telekomunikační služby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_SIPODOCUMENT">
                <annotation>
                    <documentation>Doklad o SIPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TRADECERTIFICATE">
                <annotation>
                    <documentation>Živnostenský list</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BUSREGSTATEMENT">
                <annotation>
                    <documentation>Výpis z Živnostenského rejstříku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CONCESSIONLIST">
                <annotation>
                    <documentation>Koncesní listina</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BUSACTIVCERTIF">
                <annotation>
                    <documentation>Osvědčení o podnikatelské činnosti</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BUSACTIVVERIFOT">
                <annotation>
                    <documentation>Ověření podnikatelské činnosti - jiný doklad</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_EMPLOYCONTRACT">
                <annotation>
                    <documentation>Pracovní smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INCOMEAMTCONFIRM">
                <annotation>
                    <documentation>Potvrzení o výši příjmu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INCOMEAMTCONFOTH">
                <annotation>
                    <documentation>Jiný doklad potvrzující příjem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYMENTSLIP">
                <annotation>
                    <documentation>Výplatní páska</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PENSIONASSESSMNT">
                <annotation>
                    <documentation>Důchodový výměr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_NOTICPENSIONDRAW">
                <annotation>
                    <documentation>Potvrzení o pobírání důchodu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_NOTICELONGTRMSRV">
                <annotation>
                    <documentation>Rozhodnutí o přiznání výsluhy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TAXRETURN">
                <annotation>
                    <documentation>Daňové přiznání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MOBILEPHONEBILL">
                <annotation>
                    <documentation>Výpis za mobilní telefon</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PROOFNODEBTPMTS">
                <annotation>
                    <documentation>Doklad o bezdlužnosti</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PURCHASECONTRACT">
                <annotation>
                    <documentation>Kupní smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_STATMTREALESTREG">
                <annotation>
                    <documentation>Výpis z katastru nemovitostí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MARRIAGEDOC">
                <annotation>
                    <documentation>Oddací list/Doklad o registrovaném partnerství</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CONFSTUDYLETTER">
                <annotation>
                    <documentation>Potvrzení o studiu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RENTALAGREEMENT">
                <annotation>
                    <documentation>Smlouva o pronájmu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_SECURACCOUNTSTAT">
                <annotation>
                    <documentation>Výpis z účtu cenných papírů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LICENSEAGREEMENT">
                <annotation>
                    <documentation>Licenční smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BILLOFEXCHANGE">
                <annotation>
                    <documentation>Směnka</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHERIDENTDOC">
                <annotation>
                    <documentation>Jiný identifikační doklad</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSTALLSCHEDULE">
                <annotation>
                    <documentation>Splátkový kalendář I [BF035]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSTALLSCHEDPMT">
                <annotation>
                    <documentation>Splátkový kalendář II (s úhradou) [BF055]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_REMINDERPOST">
                <annotation>
                    <documentation>Upomínka - vstup do vymáhání_pošta [BF172]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_REMOTECLIDENTIFY">
                <annotation>
                    <documentation>Dopis při vzdálené identifikaci klienta [BF524]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LOANAGRCOPY">
                <annotation>
                    <documentation>Kopie úvěrové smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_ACCNUMFORM">
                <annotation>
                    <documentation>Formulář pro uvedení čísla účtu [BF017]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PREMATUREAPPL">
                <annotation>
                    <documentation>Žádost o předčasné splacení převedeného úvěru [BF013]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LOANCLOSEDOC">
                <annotation>
                    <documentation>Doklad o doplacení úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHBANKCLOSEDOC">
                <annotation>
                    <documentation>Vyjádření druhé banky k uhrazení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_ACCCANCELCONSOL">
                <annotation>
                    <documentation>Výpověď účtu – konsolidace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PROCOMMIT">
                <annotation>
                    <documentation>Doklad k závazku (Půjčka: Úvěrová smlouva/Kreditní karta: Výpis z kreditní karty/Kontokorent: Výpis z účtu s kontokorentem)
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHERPROCOMMIT">
                <annotation>
                    <documentation>Jiný doklad o původním závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_LOANCLIENTEND">
                <annotation>
                    <documentation>Odstoupení od půjčky - přijatý od klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_TAXDOMICILE">
                <annotation>
                    <documentation>Daňový domicil</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MOBILITYPAUSE">
                <annotation>
                    <documentation>Mobilita přerušení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PEPDECLARATION">
                <annotation>
                    <documentation>Prohlášení PEP</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MOBILITYSETING">
                <annotation>
                    <documentation>Mobilita nastavení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COMPLAINTFORM">
                <annotation>
                    <documentation>Reklamační formulář</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INHERITANCE">
                <annotation>
                    <documentation>Dědictví</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_BIRTHPLACEDECL">
                <annotation>
                    <documentation>Prohlášení o místě narození</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETAPM">
                <annotation>
                    <documentation>Průvodní dopis k Dodatku - kurýrem [BF153]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CONSDECLARATION">
                <annotation>
                    <documentation>Konsolidace - prohlášení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CONSOUTGOING">
                <annotation>
                    <documentation>Odchozí konsolidace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_DECEASEDRENTRET">
                <annotation>
                    <documentation>Vrácení důchodu po zemřelém</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RESOLUTION">
                <annotation>
                    <documentation>Usnesení</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INHERITANCEAPPL">
                <annotation>
                    <documentation>Žádost o výplatu dědictví</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_ACCMNGMTCONF">
                <annotation>
                    <documentation>Potvrzení o vedení účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_ACCNEWCONFIRM">
                <annotation>
                    <documentation>Potvrzení o zřízení účtu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_AUTHRESET">
                <annotation>
                    <documentation>Reset bezpečnostních prvků [BF295]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CCSTATEMENT">
                <annotation>
                    <documentation>Výpis z KK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OVERDRAFTSTAT">
                <annotation>
                    <documentation>Výpis z KTK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PREMATLOANREPAY">
                <annotation>
                    <documentation>Žádost o předčasné splacení našeho úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_HYPOLOSSPAYEEAPP">
                <annotation>
                    <documentation>Souhlas se zrušením vinkulace pojistného plnění [BF610]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_HYPORELEASEAPPR">
                <annotation>
                    <documentation>Souhlas se zrušením zástavního práva [BF611]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MORTGAGENOTE">
                <annotation>
                    <documentation>Kopie úvěrové smlouvy hypotéky/stavebního spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSURAGREEMENT">
                <annotation>
                    <documentation>Pojistná smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_DEBTQUANTIFIC">
                <annotation>
                    <documentation>Vyčíslení dluhu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_EXTVALUATION">
                <annotation>
                    <documentation>Externí odhad</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHERMORTGAGE">
                <annotation>
                    <documentation>Jiný doklad k hypotéce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSURANCEACCESSI">
                <annotation>
                    <documentation>Přistoupení k pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSURANCECHANGE">
                <annotation>
                    <documentation>Změna pojistné smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INSURANCETERM">
                <annotation>
                    <documentation>Ukončení pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_COVERLETMLA">
                <annotation>
                    <documentation>Průvodní dopis k podpisu úvěrové smlouvy HYPO [BF641]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RHYREQQUANTIFY">
                <annotation>
                    <documentation>Žádost o vyčíslení původního závazku [BF607]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_RHYANNOTHELIEN">
                <annotation>
                    <documentation>Oznámení o vzniku zástavního práva [BF608]</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_DIVORCEVERDICT">
                <annotation>
                    <documentation>Pravomocný rozsudek o rozvodu/Pravomocné rozhodnutí soudu o zrušení partnerství</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYMENTRETURN">
                <annotation>
                    <documentation>Žádost o vrácení platby</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_MRTGQUANTIFICATION" />
            <enumeration value="DOT_INVESTMEETINGRECORD">
                <annotation>
                    <documentation>Investice - Záznam z jednání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION_CSSZ">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu ČSSZ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION_MV">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu MV</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION_MO">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu MO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION_VS">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu VS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_PAYPENSION_FOREIGN">
                <annotation>
                    <documentation>Žádost o vyplácení důchodu ZAHRANIČÍ</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_CHRUEXECUTOR">
                <annotation>
                    <documentation>Informace od exekutora – Chráněný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_OTHER_DOCUMENT">
                <annotation>
                    <documentation>Jiný dokument</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOT_INVESTMEETINGRECORD_2">
                <annotation>
                    <documentation>Záznam z jednání o investicích</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="CustomerServiceType">
        <restriction base="string">
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>current account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVINGS_ACCOUNT">
                <annotation>
                    <documentation>savings account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHILD_SAVING_ACCOUNT">
                <annotation>
                    <documentation>children's savings account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CASH_LOAN">
                <annotation>
                    <documentation>cash loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CONSOLIDATION">
                <annotation>
                    <documentation>loan refinancing</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE">
                <annotation>
                    <documentation>mortgage</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE_REF">
                <annotation>
                    <documentation>mortgage refinancing</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE_EXPENSES">
                <annotation>
                    <documentation>regular expenses insurance</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MOBILITY">
                <annotation>
                    <documentation>mobility</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DOCUMENT_ORGANIZER">
                <annotation>
                    <documentation>document organizer</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STICKER">
                <annotation>
                    <documentation>sticker</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARD">
                <annotation>
                    <documentation>payment card</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISPONENT">
                <annotation>
                    <documentation>disponent</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CARD_HOLDER">
                <annotation>
                    <documentation>card holder</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ATOM">
                <annotation>
                    <documentation>atomic client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MOBILE_APP">
                <annotation>
                    <documentation>mobile application</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT">
                <annotation>
                    <documentation>overdraft application</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVESTMENT_CERTIFICATE"/>
            <enumeration value="INVESTMENT_TO_AB"/>
            <enumeration value="FIXED_DEPOSIT"/>
        </restriction>
    </simpleType>

    <simpleType name="CustomerServiceStatusType">
        <restriction base="string">
            <enumeration value="ACTIVE">
                <annotation>
                    <documentation>client uses the service</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INACTIVE">
                <annotation>
                    <documentation>client don't uses the service</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNDETECTED">
                <annotation>
                    <documentation>service wasn't not found</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="CustomerService">
        <sequence>
            <element name="service" type="tns:CustomerServiceType" />
            <element name="status" type="tns:CustomerServiceStatusType" />
        </sequence>
    </complexType>

    <complexType name="ContractNotification">
        <sequence>
            <element name="contractNotification" type="tns:ContractNotificationType" />
            <element name="channel" type="ntf:ChannelType" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>

    <simpleType name="ContractNotificationType">
        <restriction base="string">
            <enumeration value="NOT_REALIZED_TRANSACTION" />
        </restriction>
    </simpleType>

    <simpleType name="ApplicationTypeTO">
        <restriction base="string">
            <enumeration value="RequestForOverdraft" />
            <enumeration value="RequestForTerminateOverdraft" />
            <enumeration value="RequestForInvestmentContract" />
            <enumeration value="RequestForGeneralContract" />
            <enumeration value="RequestForAccount" />
            <enumeration value="RequestForCard" />
            <enumeration value="RequestForMobility" />
            <enumeration value="RequestForSplitPayment" />
        </restriction>
    </simpleType>

    <simpleType name="ApplicationStatusTO">
        <restriction base="string">
            <enumeration value="UNDERWRITING" />
            <enumeration value="MANUALVERIFY" />
            <enumeration value="COMPLETION" />
            <enumeration value="APPROVED" />
        </restriction>
    </simpleType>

    <complexType name="CreateContractDocumentApplicationTO">
        <sequence>
            <element name="applicationType" type="tns:ApplicationTypeTO" />
            <element name="applicationId" type="long" />
            <element name="applicationStatus" type="tns:ApplicationStatusTO" />
            <element name="assistantCode" type="long" minOccurs="0" />
            <element name="productId" type="string" />
            <element name="promoFlag" type="boolean" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>True if promo is used for this application.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CreateContractDocumentCompletionResTO">
        <sequence>
            <element name="applicationId" type="long" />
            <element name="completionId" type="long" />
            <element name="completionType" type="tns:CompletionTypeTO" />
            <element name="expirationDate" type="date" />
            <element name="binDocumentId" type="string" minOccurs="0" maxOccurs="unbounded" />
        </sequence>
    </complexType>

    <complexType name="CreateAuthorizedContractDocumentTO">
        <sequence>
            <element name="cuid" type="long" />
            <element name="gcid" type="long" />
            <element name="envelopeId" type="long" />
            <element name="authorization" type="com:AuthType" />
            <element name="application" type="tns:CreateAuthorizedContractDocumentApplicationTO" maxOccurs="unbounded" />
        </sequence>
    </complexType>

    <complexType name="CreateAuthorizedContractDocumentApplicationTO">
        <sequence>
            <element name="applicationType" type="tns:ApplicationTypeTO" />
            <element name="applicationId" type="long" />
            <element name="productId" type="string" />
        </sequence>
    </complexType>

    <complexType name="PPInsuranceCreationMortgageContractDocumentContract">
        <sequence>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>CUID of client or codebtor</documentation>
                </annotation>
            </element>
            <element name="contractType" type="com:ContractType">
                <annotation>
                    <documentation>Type of contract - PP_INSURANCE_CREATE or PP_CREATION_CODEBTOR</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PPInsuranceCreationMortgageContractDocumentCompletion">
        <sequence>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>CUID of client or codebtor</documentation>
                </annotation>
            </element>
            <element name="completionId" type="long">
                <annotation>
                    <documentation>ID of created completion</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="CurrencyCode">
        <annotation>
            <documentation>3-letter ISO currency code</documentation>
        </annotation>
        <restriction base="string">
            <length value="3"></length>
            <pattern value="[A-Z]*"></pattern>
        </restriction>
    </simpleType>

    <complexType name="MonetaryAmount">
        <sequence>
            <element name="currency" type="tns:CurrencyCode">
                <annotation>
                    <documentation>Kód měny</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>Peněžní částka</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PastDueDebt">
        <sequence>
            <element name="productNumber" type="string">
                <annotation>
                    <documentation>Číslo produktu</documentation>
                </annotation>
            </element>
            <element name="origin" type="string">
                <annotation>
                    <documentation>
                        Původ pohledávky po splatnosti (MDM číselník PastDueDebtOrigin):
                        LOAN
                        MORTGAGE_LOAN
                        OVERDRAFT
                        CURRENT_ACCOUNT
                        SAVING_ACCOUNT
                        JPK_ACCOUNT
                        PAYMENT_PROTECTION_INSURANCE
                        BILL_PROTECTION_INSURANCE
                    </documentation>
                </annotation>
            </element>
            <element name="internalDPDWithTolerance" type="long">
                <annotation>
                    <documentation>Aktuální Interní DPD (DayPastDue) při zahrnutí DPD tolerance</documentation>
                </annotation>
            </element>
            <element name="pastDueAmount" type="tns:MonetaryAmount">
                <annotation>
                    <documentation>Aktuální výše dlužné částky</documentation>
                </annotation>
            </element>
            <element name="calledDue" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Příznak, zda je zesplatněno</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="GeneralContractType">
        <annotation>
            <documentation>Typ rámcové smlouvy - právní subjektivita klienta (Legal Segment)</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="RETAIL">
                <annotation>
                    <documentation>Fyzická osoba nepodnikající (FON)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTREPRENEUR">
                <annotation>
                    <documentation>Fyzická osoba podnikající (FOP)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LEGAL_ENTITY">
                <annotation>
                    <documentation>Právnická osoba (PO)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

</schema>
