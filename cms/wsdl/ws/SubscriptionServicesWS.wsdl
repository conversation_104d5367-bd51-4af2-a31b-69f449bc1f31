<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://cms.airbank.cz/ws/subscription"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://cms.airbank.cz/ws/subscription">

    <xs:annotation>
        <xs:documentation>
            SubscriptionServicesWS - The endpoint for subscription services management
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://cms.airbank.cz/ws/subscription">
            <xs:include schemaLocation="SubscriptionServices.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="searchSubscriptionServicesRequest">
        <wsdl:part element="tns:searchSubscriptionServicesRequest" name="searchSubscriptionServicesRequest"/>
    </wsdl:message>
    <wsdl:message name="searchSubscriptionServicesResponse">
        <wsdl:part element="tns:searchSubscriptionServicesResponse" name="searchSubscriptionServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="searchSubscriptionServicesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchSubscriptionServicesFault"/>
    </wsdl:message>

    <wsdl:message name="setSubscriptionServiceRequest">
        <wsdl:part element="tns:setSubscriptionServiceRequest" name="setSubscriptionServiceRequest"/>
    </wsdl:message>
    <wsdl:message name="setSubscriptionServiceResponse">
        <wsdl:part element="tns:setSubscriptionServiceResponse" name="setSubscriptionServiceResponse"/>
    </wsdl:message>
    <wsdl:message name="setSubscriptionServiceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="setSubscriptionServiceFault"/>
    </wsdl:message>

    <wsdl:portType name="SubscriptionServicesWS">
        <wsdl:operation name="searchSubscriptionServices">
            <wsdl:input message="tns:searchSubscriptionServicesRequest" name="searchSubscriptionServicesRequest"/>
            <wsdl:output message="tns:searchSubscriptionServicesResponse" name="searchSubscriptionServicesResponse"/>
            <wsdl:fault message="tns:searchSubscriptionServicesFault" name="searchSubscriptionServicesFault"/>
        </wsdl:operation>
        <wsdl:operation name="setSubscriptionService">
            <wsdl:input message="tns:setSubscriptionServiceRequest" name="setSubscriptionServiceRequest"/>
            <wsdl:output message="tns:setSubscriptionServiceResponse" name="setSubscriptionServiceResponse"/>
            <wsdl:fault message="tns:setSubscriptionServiceFault" name="setSubscriptionServiceFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="SubscriptionServicesWSSoap11" type="tns:SubscriptionServicesWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="searchSubscriptionServices">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchSubscriptionServicesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchSubscriptionServicesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchSubscriptionServicesFault">
                <soap:fault name="searchSubscriptionServicesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setSubscriptionService">
            <soap:operation soapAction=""/>
            <wsdl:input name="setSubscriptionServiceRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="setSubscriptionServiceResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="setSubscriptionServiceFault">
                <soap:fault name="setSubscriptionServiceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="SubscriptionServicesWSService">
        <wsdl:port binding="tns:SubscriptionServicesWSSoap11" name="SubscriptionServicesWSSoap11">
            <soap:address location="/ws/subscription/SubscriptionServicesWS"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>