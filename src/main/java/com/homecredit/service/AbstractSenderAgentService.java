package com.homecredit.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.model.InboxMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractSenderAgentService {

    protected final ApplicationConfig applicationConfig;
    protected final DataSource dataSource;
    protected final ObjectMapper objectMapper;

    public abstract Agent getAgent();

    /**
     * Formatter for reading timestamps from Oracle DB. Allows 0-6 decimal precision for seconds.
     */
    protected static final DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 0, 6, true)
            .toFormatter();

    /**
     * Formatter for outputting timestamps without milliseconds.
     */
    protected static final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Receive all unprocessed messages from database
     *
     * @return List of {@link InboxMessage}
     */
    protected List<Map<String, Object>> getNewRows(Agent agent) {
        log.debug("{} - Retrieving new rows from database ...", agent);
        List<Map<String, Object>> rows = new ArrayList<>();
        try (Connection conn = dataSource.getConnection()) {
            try (Statement statement = conn.createStatement();
                 ResultSet resultSet = statement.executeQuery(applicationConfig.getQuery().get(getAgent()).getGetNonProcessedRecords())) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (resultSet.next()) {
                    Map<String, Object> rowMap = new HashMap<>();

                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value;

                        int columnType = metaData.getColumnType(i);
                        if (resultSet.getObject(i) == null) {
                            value = null;
                        } else {
                            switch (columnType) {
                                case Types.INTEGER:
                                case Types.NUMERIC:
                                    value = resultSet.getLong(i);
                                    break;
                                case Types.FLOAT:
                                    value = resultSet.getFloat(i);
                                    break;
                                case Types.DOUBLE:
                                case Types.DECIMAL:
                                    value = resultSet.getDouble(i);
                                    break;
                                case Types.DATE:
                                case Types.TIMESTAMP:
                                    value = getDateTime(resultSet.getString(i));
                                    break;
                                default:
                                    value = resultSet.getString(i);
                                    break;
                            }
                        }
                        rowMap.put(columnName, value);
                    }

                    rows.add(rowMap);
                }
            }
        } catch (SQLException e) {
            log.error("{} - Exception during executing query", agent, e);
        }

        log.debug("{} - Found {} new rows for processing", agent, rows.size());
        return rows;
    }

    protected boolean isTooLateForProcessingContactId(ZonedDateTime generatedDateTime) {
        log.debug("Checking if message is too old old for processing contactId. generatedDateTime = [{}]", generatedDateTime.toString());
        return generatedDateTime.plusMinutes(applicationConfig.getContactIdTimeout())
                .isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    protected ZonedDateTime getDateTime(String string) {
        try {
            return LocalDateTime.parse(string, inputFormatter)
                    .atZone(ZoneId.of(applicationConfig.getTimezone()));
        } catch (Exception e) {
            return null;
        }
    }

    protected String parseToString(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(outputFormatter);
    }

    protected Object getColumnValue(Map<String, Object> row, String columnName) {
        try {
            return row.get(columnName.toUpperCase());
        } catch (Exception e) {
            log.error("No column with name {} found.", columnName);
            throw e;
        }
    }

    protected Boolean longToBoolean(Long number) {
        return number > 0;
    }

    protected Long getLong(String value) {
        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            return null;
        }
    }
}
