<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://homecredit.net/lcs/ws/lcscalllist"
           attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xs:element name="GetCallDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="plannedContactId" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCallDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:string" minOccurs="0"/>
                <xs:element name="externalID" type="xs:long" minOccurs="0"/>
                <xs:element name="loxonBatchId" type="xs:long" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCallDetailByTaskIdRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GetCallDetailByTaskIdResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="plannedCallId" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
