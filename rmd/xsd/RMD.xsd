<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://homecredit.net/manhattan-rmd/ws/cz/device/"
        xmlns:dev="http://homecredit.net/manhattan-rmd/ws/cz/device/">

    <simpleType name="FaultType">
        <annotation>
            <documentation>Type of error</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="GENERAL_ERROR">
                <annotation>
                    <documentation>General error</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ASSERTION_FAILED">
                <annotation>
                    <documentation>Invalid input data</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANNEL_BLOCKED">
                <annotation>
                    <documentation>Operation could not be executed - client has blocked channel of the mobile banking
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_DEVICE_COUNT_EXCEEDED">
                <annotation>
                    <documentation>It's not possible to add new device - client exceeds maximal count of devices
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NOT_FOUND">
                <annotation>
                    <documentation>Device does not exist</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ILLEGAL_DEVICE_STATUS">
                <annotation>
                    <documentation>Device has illegal state for this operation</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSUFFICIENT_DEVICE_FILTER">
                <annotation>
                    <documentation>Given filter is insufficient</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ILLEGAL_ACTIVATION_STATE">
                <annotation>
                    <documentation>Active operations are set to illegal combination of values</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ILLEGAL_CONFIRMATION_PASSWORD">
                <annotation>
                    <documentation>Illegal confirmation password</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_INVALID_CHARACTERS">
                <annotation>
                    <documentation>Illegal character used</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_LENGTH_EXCEEDED">
                <annotation>
                    <documentation>Too long name</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED">
                <annotation>
                    <documentation>Name already exists</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_ACTIVE">
                <annotation>
                    <documentation>Name already exists</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_BLOCKED">
                <annotation>
                    <documentation>Name already exists</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_REGISTERED">
                <annotation>
                    <documentation>Name already exists</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVALID_UPLOAD_TOKEN">
                <annotation>
                    <documentation>The upload token does not correspond to given installation ID.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISCARD_PRIMARY_ELEMENT_ERROR">
                <annotation>
                    <documentation>Trying to discard primary security element without force flag.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_PASSWORD_ATTEMPT_COUNT_REACHED">
                <annotation>
                    <documentation>Maximum count of attempts to enter correct login password was reached. The device
                        should be BLOCKED.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LAST_PASSWORD_ATTEMPT_REACHED">
                <annotation>
                    <documentation>User has one more chance to enter correct login password until maximum count of
                        attempts is reached.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="INVALID_NOTIFICATION_SETTING">
                <annotation>
                    <documentation>Notification sound setting is invalid. BALANCE_NOTIFICATION and CHANGE_NOTIFICATION
                        must have ID of the bank account set.
                        NOT_REALIZED_TRANSACTION cannot have the ID of the bank account set.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="PUSH_ACTIVE_4_DEVICE">
                <annotation>
                    <documentation>Discarded device is the only device where push notifications are coming. You can
                        still force discarding with the corresponding request parameter.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="UPLOAD_TOKEN_ALREADY_INITIALIZED">
                <annotation>
                    <documentation>You tried to active the upload token 'initially' (with flag initOnly set to true),
                        but it was disabled already by the user, so you cannot do that.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="ILLEGAL_SECRET_TYPE">
                <annotation>
                    <documentation>Attempt to delete primary secrets - the only supported secret type to delete is
                        BIOMETRY.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOGIN_DETAIL_NOT_FOUND">
                <annotation>
                    <documentation>There is no device bound to the session</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CAN_NOT_DISABLE">
                <annotation>
                    <documentation>Device could not be disabled.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="RMDErrorParamsTO">
        <annotation>
            <documentation>Attribute describing an error</documentation>
        </annotation>
        <sequence>
            <element name="key" type="string">
                <annotation>
                    <documentation>Attribute name</documentation>
                </annotation>
            </element>
            <element name="value" type="string">
                <annotation>
                    <documentation>Attribute value</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="RMDServiceFault">
        <annotation>
            <documentation>WebService Fault with parametrized description</documentation>
        </annotation>
        <sequence>
            <element name="type" type="dev:FaultType">
                <annotation>
                    <documentation>Error code</documentation>
                </annotation>
            </element>
            <element name="message" type="string" minOccurs="0">
                <annotation>
                    <documentation>Detailed description of error</documentation>
                </annotation>
            </element>
            <element name="params" maxOccurs="unbounded" minOccurs="0" type="dev:RMDErrorParamsTO">
                <annotation>
                    <documentation>List of attributes for description</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="DeviceStatus">
        <annotation>
            <documentation>Status of mobile device</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="REGISTERED">
                <annotation>
                    <documentation>Device exists but the definition is not paired with a physical mobile device
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="ACTIVE">
                <annotation>
                    <documentation>Device exists and paired with a physical mobile device and is ready for the mobile
                        banking
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="BLOCKED">
                <annotation>
                    <documentation>Device exists and paired but is blocked (by bank or by client), is not ready for the
                        mobile banking
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATED">
                <annotation>
                    <documentation>Device is unpaired a was deleted</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXPIRED">
                <annotation>
                    <documentation>Device was not paired in enabled interval</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="Platform">
        <annotation>
            <documentation>Mobile device platform.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ANDROID">
                <annotation>
                    <documentation>Android</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IOS">
                <annotation>
                    <documentation>iPhone</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WINDOWS_PHONE">
                <annotation>
                    <documentation>Windows Phone</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="LoginStatus">
        <annotation>
            <documentation>Result of client's login into mobile banking</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SUCCESS">
                <annotation>
                    <documentation>Client logged in</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FAILED">
                <annotation>
                    <documentation>Client was not log in</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="Result">
        <annotation>
            <documentation>Result of operation</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="OK">
                <annotation>
                    <documentation>Operation executed without any error</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANNEL_BLOCKED">
                <annotation>
                    <documentation>Operation could not be executed, client has blocked channel of mobile banking
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="MAX_DEVICE_COUNT_EXCEEDED">
                <annotation>
                    <documentation>It's not possible to add new device - client exceeds maximal count of devices
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_LENGTH_EXCEEDED">
                <annotation>
                    <documentation>Name of device is too long</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_INVALID_CHARACTERS">
                <annotation>
                    <documentation>Name of device contains invalid characters</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED">
                <annotation>
                    <documentation>Client has a device with the same name</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_ACTIVE">
                <annotation>
                    <documentation>Client has an active device with the same name</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_BLOCKED">
                <annotation>
                    <documentation>Client has a blocked device with the same name</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME_ALREADY_USED_FOR_REGISTERED">
                <annotation>
                    <documentation>Client has an non-paired device with the same name</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="OrderBy">
        <annotation>
            <documentation>Type of sorting of devices</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CUSTOMER_NAME_SURNAME">
                <annotation>
                    <documentation>by user's first name and surname</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CUSTOMER_SURNAME_NAME">
                <annotation>
                    <documentation>by user's surname and first name</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_NAME">
                <annotation>
                    <documentation>by name of device</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEVICE_STATUS">
                <annotation>
                    <documentation>by status of device</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="OrderDirection">
        <annotation>
            <documentation>Direstion of sorting</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ASC">
                <annotation>
                    <documentation>ascending sorting</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DESC">
                <annotation>
                    <documentation>descending sorting</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="Customer">
        <annotation>
            <documentation>Client</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>Client ID</documentation>
                </annotation>
            </element>
            <element name="firstName" type="string">
                <annotation>
                    <documentation>First name</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string">
                <annotation>
                    <documentation>Surname</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CustomerDetail">
        <annotation>
            <documentation>Client</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>Client ID</documentation>
                </annotation>
            </element>
            <element name="firstName" type="string">
                <annotation>
                    <documentation>First name</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string">
                <annotation>
                    <documentation>Surname</documentation>
                </annotation>
            </element>
            <element name="moratoriumBlackListFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Client is on the moratorium blacklist.</documentation>
                </annotation>
            </element>
            <element name="highRiskClientToCallFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>High risk client on the moratorium BL.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>


    <complexType name="Device">
        <annotation>
            <documentation>Mobile device</documentation>
        </annotation>
        <sequence>
            <element name="idInstallation" type="string">
                <annotation>
                    <documentation>ID (installation ID)</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>Name of device</documentation>
                </annotation>
            </element>
            <element name="deviceType" type="string" minOccurs="0">
                <annotation>
                    <documentation>Type of device</documentation>
                </annotation>
            </element>
            <element name="installationDate" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Time of installation (registration)</documentation>
                </annotation>
            </element>
            <element name="lastUsage" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Time of last usage</documentation>
                </annotation>
            </element>
            <element name="status" type="dev:DeviceStatus">
                <annotation>
                    <documentation>Status of device</documentation>
                </annotation>
            </element>
            <element name="os" type="string" minOccurs="0">
                <annotation>
                    <documentation>Operation system</documentation>
                </annotation>
            </element>
            <element name="appVersion" type="string" minOccurs="0">
                <annotation>
                    <documentation>Mobile application version</documentation>
                </annotation>
            </element>
            <element name="appVersionDetail" type="dev:AppVersionDetail" minOccurs="0">
                <annotation>
                    <documentation>Detail of mobile application version</documentation>
                </annotation>
            </element>
            <element name="activeOperations" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether are active operation enabled</documentation>
                </annotation>
            </element>
            <element name="passwordConfirmation" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether exists a password for confirmation of active operations</documentation>
                </annotation>
            </element>
            <element name="customer" type="dev:Customer" minOccurs="0">
                <annotation>
                    <documentation>Client - owner of mobile device</documentation>
                </annotation>
            </element>
            <element name="pushNotificationAllowed" type="boolean">
                <annotation>
                    <documentation>Whether this device allows push notifications to be sent to it.</documentation>
                </annotation>
            </element>
            <element name="platform" type="dev:Platform">
                <annotation>
                    <documentation>Mobile device platform</documentation>
                </annotation>
            </element>
            <element name="biometryEnabled" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether biometry authentication is enabled</documentation>
                </annotation>
            </element>
            <element name="idEnvelope" type="long" minOccurs="0">
                <annotation>
                    <documentation>Application id during on-boarding phase</documentation>
                </annotation>
            </element>
            <element name="resetOn" type="dateTime">
                <annotation>
                    <documentation>Date the device was reset</documentation>
                </annotation>
            </element>
            <element name="vInstallationId" type="string">
                <annotation>
                    <documentation>CAir virtual installation id for CASE SDK init</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceDetail">
        <annotation>
            <documentation>Mobile device</documentation>
        </annotation>
        <sequence>
            <element name="idInstallation" type="string">
                <annotation>
                    <documentation>ID (installation ID)</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>Name of device</documentation>
                </annotation>
            </element>
            <element name="deviceType" type="string" minOccurs="0">
                <annotation>
                    <documentation>Type of device</documentation>
                </annotation>
            </element>
            <element name="installationDate" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Time of installation (registration)</documentation>
                </annotation>
            </element>
            <element name="lastUsage" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Time of last usage</documentation>
                </annotation>
            </element>
            <element name="status" type="dev:DeviceStatus">
                <annotation>
                    <documentation>Status of device</documentation>
                </annotation>
            </element>
            <element name="os" type="string" minOccurs="0">
                <annotation>
                    <documentation>Operation system</documentation>
                </annotation>
            </element>
            <element name="appVersion" type="string" minOccurs="0">
                <annotation>
                    <documentation>Mobile application version</documentation>
                </annotation>
            </element>
            <element name="appVersionDetail" type="dev:AppVersionDetail" minOccurs="0">
                <annotation>
                    <documentation>Detail of mobile application version</documentation>
                </annotation>
            </element>
            <element name="activeOperations" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether are active operation enabled</documentation>
                </annotation>
            </element>
            <element name="passwordConfirmation" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether exists a password for confirmation of active operations
                    </documentation>
                </annotation>
            </element>
            <element name="customerDetail" type="dev:CustomerDetail" minOccurs="0">
                <annotation>
                    <documentation>Client - owner of mobile device</documentation>
                </annotation>
            </element>
            <element name="pushNotificationAllowed" type="boolean">
                <annotation>
                    <documentation>Whether this device allows push notifications to be sent to it.</documentation>
                </annotation>
            </element>
            <element name="platform" type="dev:Platform">
                <annotation>
                    <documentation>Mobile device platform</documentation>
                </annotation>
            </element>
            <element name="biometryEnabled" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether biometry authentication is enabled</documentation>
                </annotation>
            </element>
            <element name="idEnvelope" type="long" minOccurs="0">
                <annotation>
                    <documentation>Application id during on-boarding phase</documentation>
                </annotation>
            </element>
            <element name="resetOn" type="dateTime">
                <annotation>
                    <documentation>Date the device was reset</documentation>
                </annotation>
            </element>
            <element name="biometryDataProvided" type="boolean">
                <annotation>
                    <documentation>Flag indikující, že klient poskytl fotografii jako biometrická data při procesu
                        párování. LivenessCheck.
                    </documentation>
                </annotation>
            </element>
            <element name="devicePairing" type="dev:DevicePairingInfo" minOccurs="0"/>
        </sequence>
    </complexType>


    <complexType name="DeviceHistory">
        <annotation>
            <documentation>History of changes of device</documentation>
        </annotation>
        <complexContent>
            <extension base="dev:Device">
                <sequence>
                    <element name="modifTime" type="dateTime">
                        <annotation>
                            <documentation>Time of change</documentation>
                        </annotation>
                    </element>
                    <element name="cuidUser" type="long">
                        <annotation>
                            <documentation>Client ID of modifying user</documentation>
                        </annotation>
                    </element>
                    <element name="cuidManager" type="string" minOccurs="0">
                        <annotation>
                            <documentation>ID of modifying operator(ID in LDAP)</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DeviceLogin">
        <annotation>
            <documentation>Result of client's login into mobile banking</documentation>
        </annotation>
        <sequence>
            <element name="insTime" type="dateTime">
                <annotation>
                    <documentation>Time of login</documentation>
                </annotation>
            </element>
            <element name="status" type="dev:LoginStatus">
                <annotation>
                    <documentation>Result of login</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceLoginHistory">
        <annotation>
            <documentation>History of login into mobile banking</documentation>
        </annotation>
        <sequence>
            <element name="loginRecord" type="dev:DeviceLogin" maxOccurs="unbounded" minOccurs="0">
                <annotation>
                    <documentation>Result of login into mobile banking</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceStatusHistory">
        <annotation>
            <documentation>History of status of device</documentation>
        </annotation>
        <sequence>
            <element name="status" type="dev:DeviceStatus">
                <annotation>
                    <documentation>Status of device</documentation>
                </annotation>
            </element>
            <element name="modifTime" type="dateTime">
                <annotation>
                    <documentation>Time of change</documentation>
                </annotation>
            </element>
            <element name="cuidUser" type="long">
                <annotation>
                    <documentation>Client ID of modifying user</documentation>
                </annotation>
            </element>
            <element name="cuidManager" type="string" minOccurs="0">
                <annotation>
                    <documentation>ID of modifying operator(ID in LDAP)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceInfo">
        <annotation>
            <documentation>Description of mobile device - including of secret information for the verifying of paired
                device
            </documentation>
        </annotation>
        <sequence>
            <element name="idCustomer" type="long" minOccurs="0">
                <annotation>
                    <documentation>Client ID</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>Name of device</documentation>
                </annotation>
            </element>
            <element name="salt" type="string" minOccurs="0">
                <annotation>
                    <documentation>Salt</documentation>
                </annotation>
            </element>
            <element name="verifier" type="string" minOccurs="0">
                <annotation>
                    <documentation>Verifier</documentation>
                </annotation>
            </element>
            <element name="status" type="string">
                <annotation>
                    <documentation>Status of device</documentation>
                </annotation>
            </element>
            <element name="biometryVerifier" type="string" minOccurs="0">
                <annotation>
                    <documentation>biometry verifier</documentation>
                </annotation>
            </element>
            <element name="biometrySalt" type="string" minOccurs="0">
                <annotation>
                    <documentation>biometry salt</documentation>
                </annotation>
            </element>
            <element name="initializedForLogin" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Indicates whether the device is registerd in OBS</documentation>
                </annotation>
            </element>
            <element name="idEnvelope" type="long" minOccurs="0">
                <annotation>
                    <documentation>Envelope identifier during on-boarding phase</documentation>
                </annotation>
            </element>
            <element name="threatsAvoidanceToken" type="string">
                <annotation>
                    <documentation>Threats avoidance token</documentation>
                </annotation>
            </element>
            <element name="activeOperations" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether are active operation enabled</documentation>
                </annotation>
            </element>
            <element name="resetOn" type="date">
                <annotation>
                    <documentation>Date the device was reset</documentation>
                </annotation>
            </element>
            <element name="passwordSetInVersion" type="string">
                <annotation>
                    <documentation>Application version when the password was set</documentation>
                </annotation>
            </element>
            <element name="nfcEnabled" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether NFC payments are enabled</documentation>
                </annotation>
            </element>
            <element name="initialPreviewMode" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag specifying whether the device was initialized in preview mode.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceRegistrationInfo">
        <annotation>
            <documentation>Description of mobile device - (before paring of device)</documentation>
        </annotation>
        <complexContent>
            <extension base="dev:DeviceInfo">
                <sequence>
                    <element name="idInstallation" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Installation ID</documentation>
                        </annotation>
                    </element>
                    <element name="regExpiration" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>Time of expiration - device must be paired to this moment</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DeviceAbilityInfo">
        <annotation>
            <documentation>Description of mobile device abilities and its operation system settings</documentation>
        </annotation>
        <sequence>
            <element name="nfc" type="boolean">
                <annotation>
                    <documentation>Device has NFC chip</documentation>
                </annotation>
            </element>
            <element name="biometry" type="boolean">
                <annotation>
                    <documentation>Device can authenticate user with strong biometry</documentation>
                </annotation>
            </element>
            <element name="pushNotificationAccepted" type="boolean">
                <annotation>
                    <documentation>Device has push notifications enabled</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DevicePairingInfo">
        <annotation>
            <documentation>Specific data which identifies hardware device - android\ios hw token, etc.</documentation>
        </annotation>
        <sequence>
            <element name="sourceIp" type="string"/>
            <element name="languageOs" type="string"/>
            <element name="screenResolution" type="string"/>
            <element name="androidId" type="string"/>
            <element name="androidGsfid" type="string"/>
            <element name="androidHwfp" type="string"/>
            <element name="iosVendorId" type="string"/>
            <element name="deviceProducer" type="string"/>
            <element name="deviceUserName" type="string"/>
            <element name="installedApplications" type="string"/>
        </sequence>
    </complexType>

    <complexType name="DeviceOperationsSettings">
        <annotation>
            <documentation>Settings of active operations</documentation>
        </annotation>
        <sequence>
            <element name="activeOperations" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether are active operation enabled</documentation>
                </annotation>
            </element>
            <element name="passwordConfirmation" type="boolean">
                <annotation>
                    <documentation>Flag specifying whether exists a password for confirmation of active operations
                    </documentation>
                </annotation>
            </element>
            <element name="initialPreviewMode" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag specifying whether the device was initialized in preview mode.</documentation>
                </annotation>
            </element>
            <element name="pushNotificationAllowed" type="boolean">
                <annotation>
                    <documentation>Whether this device allows push notifications to be sent to it.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceFilter">
        <annotation>
            <documentation>Filter for searching of mobile devices</documentation>
        </annotation>
        <sequence>
            <element maxOccurs="unbounded" minOccurs="0" name="cuid" type="long">
                <annotation>
                    <documentation>List of client IDs</documentation>
                </annotation>
            </element>
            <element maxOccurs="unbounded" minOccurs="0" name="envelopeId" type="long">
                <annotation>
                    <documentation>List of envelope IDs</documentation>
                </annotation>
            </element>
            <element maxOccurs="unbounded" minOccurs="0" name="status" type="dev:DeviceStatus">
                <annotation>
                    <documentation>List of device's statuses</documentation>
                </annotation>
            </element>
            <element minOccurs="0" name="pushNotificationAllowed" type="boolean">
                <annotation>
                    <documentation>Filter by pushNotificationAllowed attribute of devices. Devices with
                        pushNotificationAllowed allow push notifications to be sent to them.
                        If this attribute is not filled, than the filter is not applied.
                    </documentation>
                </annotation>
            </element>
            <element minOccurs="0" name="orderBy" type="dev:OrderBy">
                <annotation>
                    <documentation>Sorting type</documentation>
                </annotation>
            </element>
            <element minOccurs="0" name="orderDirection" type="dev:OrderDirection">
                <annotation>
                    <documentation>Sort direction</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceNotificationSoundSetting">
        <annotation>
            <documentation>Push notification sound settings for a device.</documentation>
        </annotation>
        <sequence>
            <element name="deviceId" type="string">
                <annotation>
                    <documentation>Device installation ID</documentation>
                </annotation>
            </element>
            <element minOccurs="0" maxOccurs="unbounded" name="notificationSoundSettings"
                     type="dev:NotificationSoundSetting">
                <annotation>
                    <documentation>All notification sound settings set for given device.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeviceNotificationSoundSettingWithDefaults">
        <annotation>
            <documentation>Push notification sound settings for a device with default settings.</documentation>
        </annotation>
        <complexContent>
            <extension base="dev:DeviceNotificationSoundSetting">
                <sequence>
                    <element minOccurs="3" maxOccurs="3" name="notificationSoundDefault"
                             type="dev:NotificationSoundDefault">
                        <annotation>
                            <documentation>Notification sound defaults. They will always be filled for every push
                                notification type.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="NotificationSoundSetting">
        <annotation>
            <documentation>Push notification sound settings.</documentation>
        </annotation>
        <complexContent>
            <extension base="dev:NotificationSoundDefault">
                <sequence>
                    <element minOccurs="0" name="idBankAccount" type="long">
                        <annotation>
                            <documentation>Bank account ID in case of account push notification types
                                (CHANGE_NOTIFICATION or BALANCE_NOTIFICATION).
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="NotificationType">
        <annotation>
            <documentation>Type of a push notification</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CHANGE_NOTIFICATION">
                <annotation>
                    <documentation>Bank account change notification.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BALANCE_NOTIFICATION">
                <annotation>
                    <documentation>Bank account balance notification.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_REALIZED_TRANSACTION">
                <annotation>
                    <documentation>Notification about not realized transaction.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="NotificationSoundDefault">
        <annotation>
            <documentation>Push notification sound settings default values.</documentation>
        </annotation>
        <sequence>
            <element name="notificationType" type="dev:NotificationType">
                <annotation>
                    <documentation>Type of the push notification</documentation>
                </annotation>
            </element>
            <element minOccurs="0" name="pushNotificationSound" type="string">
                <annotation>
                    <documentation>Name of the sound file of the push notification.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <!-- SecurityContext je kompletne prevzaty z OBS  -->
    <!-- SecurityContext bude zrusen / resp. nahrazen novym contextem  -->
    <!-- Tento typ by v RMD nemel byt a mel by byt v orchestratoru na OSB ...  -->
    <complexType name="SecurityContext">
        <annotation>
            <documentation>Obsah předávaný v hlavičce každé volané WS metody. Určuje kontext pro OBS.</documentation>
        </annotation>
        <sequence>
            <element type="long" name="cuid" minOccurs="0">
                <annotation>
                    <documentation>id klienta</documentation>
                </annotation>
            </element>
            <element type="string" name="trackingID">
                <annotation>
                    <documentation>tracking id</documentation>
                </annotation>
            </element>
            <element type="string" name="operatorCUID" minOccurs="0">
                <annotation>
                    <documentation>identifikace operátora</documentation>
                </annotation>
            </element>
            <element type="string" name="languageIsoCode">
                <annotation>
                    <documentation>ISO kód jazyka. Např. cs pro češtinu.</documentation>
                </annotation>
            </element>
            <element name="channelCode">
                <annotation>
                    <documentation>
                        Kód kanálu.

                        IB - veřejný normální IB
                        BRANCH - tento kód se použije pouze v případě, že klient je na pobočce
                        a je zároveň přihlášen pobočník !!! Pokud klient pracuje na pobočce a není přihlášen s
                        pobočníkem, potom se používá kód IB !!!
                        ECC - externí call centrum
                        ICC - interní call centrum
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="IB"/>
                        <enumeration value="BRANCH"/>
                        <enumeration value="ECC"/>
                        <enumeration value="ICC"/>
                    </restriction>
                </simpleType>
            </element>
            <element type="long" name="idProfile" minOccurs="0">
                <annotation>
                    <documentation>
                        id profilu - id vazby mezi osobou a rámcovou
                        službou - získá se z objektu
                        GeneralContractTO.idProfile
                    </documentation>
                </annotation>
            </element>
            <element name="certificateId" type="string" minOccurs="0">
                <annotation>
                    <documentation>ID certifikátu pobočky</documentation>
                </annotation>
            </element>
            <element name="branchCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>Kód pobočky</documentation>
                </annotation>
            </element>
            <element name="authType" type="string" minOccurs="0">
                <annotation>
                    <documentation>Kód autorizačního typu - slouží pouze pro potřeby logování na OSB - OBS ho ignoruje
                    </documentation>
                </annotation>
            </element>
            <element name="UUID" type="string" minOccurs="0">
                <annotation>
                    <documentation>jednoznační identifikátor - pro potřeby logování na OSB - OBS ignoruje
                    </documentation>
                </annotation>
            </element>
            <element name="getAuthTypeTime" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>čas volání metody getAuthType - pro potřeby logování na OSB - OBS ignoruje
                    </documentation>
                </annotation>
            </element>
            <element name="installationID" type="string" minOccurs="0">
                <annotation>
                    <documentation>jednoznačná identifikace mobilního zařízení - posílá se vždy pro kanál SPB
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="SecretType">
        <annotation>
            <documentation>Type of secret.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PASSWORD">
                <annotation>
                    <documentation>Primary secret.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BIOMETRY">
                <annotation>
                    <documentation>Secret for biometry.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="MobileKeys">
        <annotation>
            <documentation>
                Mobile keys used to secure the communication during subsequent remote management sessions.
            </documentation>
        </annotation>
        <sequence>
            <element name="transportKey" type="base64Binary">
                <annotation>
                    <documentation>The Mobile Transport Key used to provide confidentiality of data at the transport
                        level between the Mobile Payment App and MDES
                    </documentation>
                </annotation>
            </element>
            <element name="macKey" type="base64Binary">
                <annotation>
                    <documentation>The Mobile MAC Key used to provide integrity of data at the transport level between
                        the Mobile Payment App and MDES
                    </documentation>
                </annotation>
            </element>
            <element name="dataEncryptionKey" type="base64Binary">
                <annotation>
                    <documentation>The Mobile Data Encryption Key used to encrypt any sensitive data at the data field
                        level between the Mobile Payment App and MDES
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="AppVersionDetail">
        <sequence>
            <element name="fullVersion" type="string">
                <annotation>
                    <documentation>Unparsed version of application.</documentation>
                </annotation>
            </element>
            <element name="mainVersion" type="int">
                <annotation>
                    <documentation>Main part of fullVersion.</documentation>
                </annotation>
            </element>
            <element name="mainVersionPatch" type="int">
                <annotation>
                    <documentation>Patch part of fullVersion.</documentation>
                </annotation>
            </element>
            <element name="mainVersionSubPatch" type="int">
                <annotation>
                    <documentation>Sub-path part of fullVersion.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <element name="securityContext" type="dev:SecurityContext"/>
    <element name="RMDServiceFault" type="dev:RMDServiceFault"/>

</schema>