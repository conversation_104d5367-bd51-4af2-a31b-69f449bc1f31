<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:phi="http://airbank.cz/lap/ws/phishingWhiteList" targetNamespace="http://airbank.cz/lap/ws/phishingWhiteList">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/lap/ws/phishingWhiteList">
                <xsd:include schemaLocation="../xsd/PhishingWhiteListWS.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="GetClientsVPNIPsRequest">
            <wsdl:part element="phi:GetClientsVPNIPsRequest" name="GetClientsVPNIPsRequest" />
        </wsdl:message>
        <wsdl:message name="GetClientsVPNIPsResponse">
            <wsdl:part element="phi:GetClientsVPNIPsResponse" name="GetClientsVPNIPsResponse" />
        </wsdl:message>

        <wsdl:message name="GetReferersRequest">
            <wsdl:part element="phi:GetReferersRequest" name="GetReferersRequest" />
        </wsdl:message>
        <wsdl:message name="GetReferersResponse">
            <wsdl:part element="phi:GetReferersResponse" name="GetReferersResponse" />
        </wsdl:message>

        <wsdl:message name="GetBrowserLanguagesRequest">
            <wsdl:part element="phi:GetBrowserLanguagesRequest" name="GetBrowserLanguagesRequest" />
        </wsdl:message>
        <wsdl:message name="GetBrowserLanguagesResponse">
            <wsdl:part element="phi:GetBrowserLanguagesResponse" name="GetBrowserLanguagesResponse" />
        </wsdl:message>

        <wsdl:message name="GetCountriesRequest">
            <wsdl:part element="phi:GetCountriesRequest" name="GetCountriesRequest" />
        </wsdl:message>
        <wsdl:message name="GetCountriesResponse">
            <wsdl:part element="phi:GetCountriesResponse" name="GetCountriesResponse" />
        </wsdl:message>

        <wsdl:message name="GetIPsRequest">
            <wsdl:part element="phi:GetIPsRequest" name="GetIPsRequest" />
        </wsdl:message>
        <wsdl:message name="GetIPsResponse">
            <wsdl:part element="phi:GetIPsResponse" name="GetIPsResponse" />
        </wsdl:message>

        <wsdl:message name="GetCuidsRequest">
            <wsdl:part element="phi:GetCuidsRequest" name="GetCuidsRequest" />
        </wsdl:message>
        <wsdl:message name="GetCuidsResponse">
            <wsdl:part element="phi:GetCuidsResponse" name="GetCuidsResponse" />
        </wsdl:message>

        <wsdl:message name="GetCookiesRequest">
            <wsdl:part element="phi:GetCookiesRequest" name="GetCookiesRequest" />
        </wsdl:message>
        <wsdl:message name="GetCookiesResponse">
            <wsdl:part element="phi:GetCookiesResponse" name="GetCookiesResponse" />
        </wsdl:message>

        <wsdl:portType name="PhishingWhiteListWS">

            <wsdl:operation name="GetClientsVPNIPs">
                <wsdl:input message="phi:GetClientsVPNIPsRequest" />
                <wsdl:output message="phi:GetClientsVPNIPsResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetReferers">
                <wsdl:input message="phi:GetReferersRequest" />
                <wsdl:output message="phi:GetReferersResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetBrowserLanguages">
                <wsdl:input message="phi:GetBrowserLanguagesRequest" />
                <wsdl:output message="phi:GetBrowserLanguagesResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetCountries">
                <wsdl:input message="phi:GetCountriesRequest" />
                <wsdl:output message="phi:GetCountriesResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetIPs">
                <wsdl:input message="phi:GetIPsRequest" />
                <wsdl:output message="phi:GetIPsResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetCuids">
                <wsdl:input message="phi:GetCuidsRequest" />
                <wsdl:output message="phi:GetCuidsResponse" />
            </wsdl:operation>

            <wsdl:operation name="GetCookies">
                <wsdl:input message="phi:GetCookiesRequest" />
                <wsdl:output message="phi:GetCookiesResponse" />
            </wsdl:operation>

        </wsdl:portType>



        <wsdl:binding name="PhishingWhiteListBinding" type="phi:PhishingWhiteListWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="GetClientsVPNIPs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetReferers">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetBrowserLanguages">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetCountries">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetIPs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetCuids">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetCookies">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="PhishingWhiteListWS">
            <wsdl:port binding="phi:PhishingWhiteListBinding" name="PhishingWhiteListtBinding">
                <soap:address location="/lap/ext/WhiteListProcessWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
