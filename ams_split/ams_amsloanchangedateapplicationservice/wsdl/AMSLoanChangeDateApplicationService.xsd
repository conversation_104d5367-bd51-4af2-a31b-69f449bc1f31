<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:auth="http://airbank.cz/ams/ws/application/authorization/"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/loanChangeDate"
           targetNamespace="http://airbank.cz/ams/ws/application/loanChangeDate">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/authorization/" schemaLocation="../xsd/Authorization.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="loanId" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of loan to change.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeDateRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeDateResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing ChangeDateTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="possibleInstallmentDays" type="xs:int" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible payment days as input for combo.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedInstallmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Payment day from application. Null, if not set by user previously.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="originalLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Original loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="updatedLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Updated loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeDateRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="instalmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Selected installment day.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeDateResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating ChangeDateTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="possibleInstallmentDays" type="xs:int" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible payment days as input for combo.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedInstallmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Payment day from application. Null, if not set by user previously.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="originalLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Original loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="updatedLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Updated loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Change accepted.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>If change has not been accepted, reason why.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="installmentAmountCorrected" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Installment amount has been corrected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="numberOfInstallmentsCorrected" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Number of installments has been corrected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="loanInstallmentCountCorrected" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Installment count has been corrected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of summary page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="originalLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Original loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="updatedLoan" type="loanCommon:LoanTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Updated loan details.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of summary page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="authentication" type="auth:AuthType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Solved OBS authentication.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="accepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Change accepted.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>If change has not been accepted, reason why.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetBonusStepsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetBonusStepsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="activeBonusVariantIndex" type="xs:int" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Active bonus variant index in array bonusVariants.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="bonusVariants" type="loanCommon:LoanBonusTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of bonus variants. They have order numbers, 0 is standard, 1..n are bonus variants. Lower number means better
                            bonus.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="bonusLoss" type="xs:boolean" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Flag indicating if any of bonus steps passed its validity.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>